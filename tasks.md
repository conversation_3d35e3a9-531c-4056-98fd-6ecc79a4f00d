# G-codeビューワー拡張機能 開発計画書 (改訂版)

## 📋 プロジェクト概要

3Dプリンティング用およびCNC加工用のG-codeファイルを、単一のビューワー上で同時に表示、比較、分析できる拡張機能を開発します。本計画書は、AIによる実装を想定し、各タスクを詳細なステップに分解しています。

## 🎯 プロジェクト目標

  - 最大2つ（将来的にはそれ以上）のG-codeファイルを同時に読み込み、表示する機能の実装。
  - ファイル種別（3Dプリント/CNC）に応じた適切なパス描画ロジックの適用。
  - ファイルごとの明確な色分け（例：3Dプリント：青系、CNC：赤系）。
  - 各ファイルのレイヤーや可視性を独立して制御できるUIの提供。
  - 将来的な機能拡張（3ファイル以上の対応、高度な分析機能）を容易にするスケーラブルな設計。

-----

## 📅 Phase 1: 基盤設計・データ構造の拡張

### Step 1.1: コアデータ構造の設計と定義

  * **目的:** 複数のG-codeファイルとその状態を管理するための基本的なデータ構造を定義します。

  * **タスクリスト:**

      - [x] **`GCodeFileType`列挙型の定義:**

          - `GCodeProcessor.hpp`に、ファイルの種類を識別するための`GCodeFileType`列挙型を定義しました。
          - 値: `PRINT`, `CNC`, `UNKNOWN`。
          - `detect_file_type()`関数も実装し、G-codeの内容からファイル種別を自動判定できるようにしました。

      - [x] **`GCodeFileData`構造体の設計:**

          - 複数ファイル管理の核となる`GCodeFileData`構造体を`GCodeViewer.hpp`内に定義しました。
          - ```cpp
            struct GCodeFileData {
                // --- ファイル情報 ---
                int id;                         // 一意な識別ID
                std::string filename;           // 元のファイル名
                std::string display_name;       // UIに表示する名前 (編集可能)
                GCodeFileType type;             // ファイル種別 (PRINT, CNC, UNKNOWN)

                // --- 描画と状態管理 ---
                bool visible;                   // このファイルを表示するかどうかのフラグ
                bool enabled;                   // このファイルが有効かどうかのフラグ
                ColorRGBA display_color;        // このファイルの描画色

                // --- G-codeデータとビューワー ---
                GCodeProcessorResult gcode_result; // パース結果の生データ
                libvgcode::Viewer viewer;       // このファイル専用のレンダリングインスタンス

                // --- レイヤー制御情報 ---
                std::vector<double> layers_zs;      // 各レイヤーのZ座標
                std::vector<float> layers_times;   // 各レイヤーまでの経過時間

                // コンストラクタ
                GCodeFileData() : id(0), type(GCodeFileType::UNKNOWN), visible(true), enabled(true), 
                                 display_color(ColorRGBA::BLUE()) {}
            };
            ```

      - [x] **`GCodeViewer`クラスの拡張:**

          - `GCodeViewer.hpp`の`GCodeViewer`クラス内に、複数の`GCodeFileData`を保持するためのデータメンバーを追加しました。
          - `std::vector<GCodeFileData> m_gcode_files;`
          - ファイルIDを管理するためのカウンター変数を追加しました。
          - `int m_next_file_id{ 0 };`
          - 複数ファイル管理のためのパブリックメソッドも追加しました：
            - `add_gcode_file()`: 新しいファイルを追加
            - `remove_gcode_file()`: ファイルを削除
            - `toggle_file_visibility()`: ファイルの可視性を切り替え
            - `set_file_color()`: ファイルの色を設定
            - `set_file_display_name()`: ファイルの表示名を設定
            - `get_gcode_files()`: 全ファイルのリストを取得
            - `get_gcode_file()`: 特定のファイルデータを取得
      
      - [x] **今回のステップが問題なく実装できてるか確認**

### Step 1.2: `libvgcode`ライブラリの調査と検証

  * **目的:** `libvgcode`が複数インスタンスでの利用に耐えうるか、パフォーマンス上のボトルネックがないかを確認します。

  * **タスクリスト:**

      - [x] **複数Viewerインスタンスの同時使用テスト:**
          - テスト用のコードを作成し、`libvgcode::Viewer`のインスタンスを2つ生成します。
          - それぞれのインスタンスに異なる単純なG-codeデータを読み込ませ、別々のウィンドウまたは領域に同時にレンダリングできるか検証します。
          - `tests/libslic3r/test_multiple_viewers.cpp`に包括的なテストケースを実装しました。
          - `sandboxes/multiple_viewers_test/main.cpp`に実際のOpenGLコンテキストを使用したテストプログラムを作成しました。
      - [x] **メモリ使用量の評価:**
          - 上記のテストコードを実行し、Viewerインスタンスを1つ追加するごとのメモリ使用量の増加を測定・記録します。
          - `src/slic3r/GUI/MemoryProfiler.hpp`と`MemoryProfiler.cpp`にプラットフォーム固有のメモリ測定機能を実装しました。
          - Windows、macOS、Linuxの各プラットフォームに対応したメモリ使用量測定機能を提供します。
      - [x] **レンダリングパフォーマンスの検証:**
          - 各Viewerインスタンスのレンダリングループにかかる時間を測定し、複数インスタンス化によるフレームレートへの影響を評価します。
          - パフォーマンステスト、ストレステスト、データ一貫性テストを含む包括的なテストスイートを実装しました。

      - [x] **今回のステップが問題なく実装できてるか確認**
-----

## 📅 Phase 2: ファイル読み込み機能の拡張

### Step 2.1: UIの拡張 (複数ファイル対応)

  * **目的:** ユーザーが3Dプリント用とCNC用のG-codeファイルを明確に区別して読み込めるようにUIを改良します。

  * **タスクリスト:**

      - [x] **ファイルメニューの変更:**
          - 既存の「G-codeを開く」メニュー項目を以下のように変更・追加しました。
          - 「3Dプリント G-codeを開く...」
          - 「CNC G-codeを開く...」
      - [x] **`Plater::load_gcode()`メソッドの拡張:**
          - `Plater::load_gcode(const wxString& filename, GCodeFileType file_type)` のように、ファイル種別を引数で受け取れるようにシグネチャを変更しました。
      - [x] **メニューイベントハンドラの実装:**
          - 「3Dプリント G-codeを開く...」が選択された場合、`load_gcode()`を`GCodeFileType::PRINT`で呼び出します。
          - 「CNC G-codeを開く...」が選択された場合、`load_gcode()`を`GCodeFileType::CNC`で呼び出します。
      - [x] **ドラッグ＆ドロップ機能の拡張:**
          - G-codeファイルがドラッグ＆ドロップされた際に、「3Dプリントとして読み込む」「CNCとして読み込む」を選択させるモーダルダイアログ（GCodeFileTypeDialog）を表示する機能を実装しました。
      - [x] **ファイル管理UIパネルの作成:**
          - 読み込まれたファイルの一覧を表示するUIパネルをImGuiで作成しました。
          - パネルには、ファイル名 (`display_name`)、種別アイコン、可視性チェックボックス、色選択、削除ボタンを表示します。
          - 全ファイルの一括操作（表示/非表示/削除）機能も実装しました。

      - [x] **今回のステップが問題なく実装できてるか確認**

### Step 2.2: ファイル処理ロジックの拡張とCNC対応

  * **目的:** 複数ファイルを効率的に処理し、特にCNC G-codeを正しく解釈して描画可能なデータに変換するロジックを実装します。

  * **タスクリスト:**

      - [x] **G-code自動判定機能の実装:**
          - `GCodeProcessor`内に、G-codeの内容からファイル種別を推測する`GCodeFileType detect_file_type(const std::string& gcode_content)`関数を実装しました。
          - 3Dプリントの判定ロジック: `M104`, `M109`, `M140`, `M190` (温度設定) や `E` パラメータが頻出するかどうかで判定します。
          - CNCの判定ロジック: 上記が存在せず、`G0`, `G1`, `G2`, `G3`が主体であるかで判定します。
      - [x] **`GCodeProcessor`のCNCパス抽出ロジックの追加:**
          - G-codeをパースするループ内に、ファイル種別 (`GCodeFileType`) に基づく条件分岐を追加しました。
          - **もし`type`が`PRINT`の場合:** 既存のロジック通り、`E`値が正の`G1`コマンドを押し出しパスとして処理します。
          - **もし`type`が`CNC`の場合:** `G1`, `G2`, `G3`コマンドをツールパス（切削パス）として記録します。`G0`コマンドは早送りパスとして別途記録します。`E`値は無視します。
      - [x] **複数ファイル処理の非同期化:**
          - `GCodeMultiProcessor`クラスを作成しました。
          - 複数のG-codeファイルが読み込まれた際に、各ファイルのパース処理を`std::async`を用いて非同期で実行するロジックを実装しました。これにより、UIのフリーズを防ぎます。
      - [x] **座標系の統一処理:**
          - 全てのファイルが読み込まれた後、各ファイルのバウンディングボックスを計算する`unify_coordinate_systems()`メソッドを実装しました。
          - 全てのファイルがビューの中心に収まるように、グローバルなビューポート行列を計算する`calculate_global_viewport_matrix()`関数を実装しました。

      - [x] **今回のステップが問題なく実装できてるか確認**

-----

## 📅 Phase 3: レンダリングシステムの拡張

### Step 3.1: 複数ファイルの統合レンダリング

  * **目的:** 複数のG-codeデータを、それぞれの色や状態で単一の3Dシーンに正しくレンダリングします。

  * **タスクリスト:**

      - [ ] **`GCodeViewer::render()`メソッドの改修:**
          - 現在の単一ファイル描画ロジックを、`m_gcode_files`ベクターをループ処理する形に書き換えました。
          - 複数ファイルが存在する場合は`render_multiple_files()`を呼び出し、単一ファイルの場合は既存の`render_toolpaths()`を使用する後方互換性を維持しました。
      - [ ] **`render_multiple_files()`メソッドの実装:**
          - 複数ファイルのレンダリングを行う新しいメソッドを実装しました。
          - 各ファイルのビューワーインスタンス（`file->viewer`）を順番に呼び出します。
      - [ ] **可視性フラグの適用:**
          - ループ内で、各`GCodeFileData`インスタンスの`visible`および`enabled`フラグを確認し、`false`の場合はそのファイルのレンダリングをスキップします。
      - [ ] **座標オフセットの適用:**
          - 各ファイルの`coordinate_offset`を使用して、ファイルごとの座標変換行列を計算し、統合された座標系での描画を実現しました。
      - [ ] **深度バッファの管理:**
          - 各ファイルのレンダリング前に深度テストを有効にし、複数のオブジェクトが正しい前後関係を保って描画されるようにしました。
      - [ ] **色設定の準備:**
          - `libvgcode::Viewer`は個別のファイル色設定をサポートしていないことを確認し、将来の実装に向けてコメントを追加しました。
          - `extrusion_role_color`や`option_color`を使用した色分けは今後実装予定です。

      - [ ] **今回のステップが問題なく実装できてるか確認**

### Step 3.2: パフォーマンス最適化

  * **目的:** 複数ファイルを扱っても、快適な操作性を維持するための最適化を実装します。

  * **タスクリスト:**

      - [ ] **インスタンス描画の活用調査:**
          - `libvgcode`がインスタンス描画をサポートしているか確認します。サポートしている場合、同じ種類の線分（例：押し出しパス、移動パス）をファイル間でまとめて描画することで、ドローコールを削減する手法を検討・実装します。
      - [ ] **LOD (Level of Detail) の簡易実装:**
          - カメラのズームレベルに応じて、描画する線分の間引きを行う機能を実装します。カメラが遠い場合は、一部の線分を描画しないことで負荷を軽減します。
      - [ ] **フラスタムカリングの確認:**
          - `libvgcode`のビューワーが視錐台（フラスタム）カリングを適切に行っているか確認します。行われていない場合、ビューの外にあるオブジェクトを描画しないようにする処理を追加します。
      - [ ] **パフォーマンス監視UIの追加:**
          - デバッグモード時、画面の隅に現在のフレームレート(FPS)、総メモリ使用量、読み込みファイル数、総描画頂点数を表示するUIを追加します。
      - [ ] **今回のステップが問題なく実装できてるか確認**


-----

## 📅 Phase 4: UI/UX機能の実装

### Step 4.1: 独立したレイヤー制御UIの実装

  * **目的:** ユーザーが各ファイルの表示レイヤーを個別に、または同期して直感的に操作できるUIを提供します。

  * **タスクリスト:**

      - [ ] **`MultiFileLayerSlider`クラスの実装:**
          - ファイル管理パネル内に、読み込まれたファイルごとにImGuiのスライダーを動的に生成します。
          - 各スライダーは、対応する`GCodeFileData`のレイヤー範囲を制御します。
      - [ ] **レイヤー同期機能の実装:**
          - 「レイヤーを同期」チェックボックスをUIに追加します。
          - チェックが入っている場合、1つのスライダーを操作すると、他のすべてのファイルが同じレイヤー番号（または正規化された高さの割合）に追従するようにイベントハンドラを実装します。
      - [ ] **レイヤー情報表示パネルの実装:**
          - 現在のスライダー位置における、各ファイルのレイヤー番号、Z高さ、経過時間などの情報を表示するテキストパネルを作成します。

      - [ ] **今回のステップが問題なく実装できてるか確認**
### Step 4.2: 可視性・凡例UIの実装

  * **目的:** 各ファイルの表示/非表示や外観を簡単に管理できるUIを提供します。

  * **タスクリスト:**

      - [ ] **可視性制御UIの実装:**
          - ファイル管理UIパネル内の各ファイル項目の横に、目のアイコンまたはチェックボックスを配置し、クリックで`GCodeFileData`の`visible`フラグを切り替えられるようにします。
      - [ ] **透明度調整スライダーの追加:**
          - ファイルごとに透明度を調整できるスライダーを追加し、`display_color`のアルファ値を変更できるようにします。
      - [ ] **凡例（Legend）パネルの実装:**
          - ビューポートの隅に凡例パネルを表示します。
          - パネルには、各ファイルの`display_name`と、その`display_color`を示す色の四角形を一覧で表示します。
      - [ ] **今回のステップが問題なく実装できてるか確認**
-----

## 📅 Phase 5: 高度な機能と仕上げ

### Step 5.1: 比較・分析機能

  * **目的:** 2つのモデル間の関係性を視覚的に分析する初歩的な機能を提供します。

  * **タスクリスト:**

      - [ ] **干渉チェック機能の実装:**
          - 2つのファイルのツールパスが空間的に交差（干渉）している箇所を検出し、異なる色でハイライト表示する機能を実装します。（アルゴリズム開発）
      - [ ] **統計情報の統合表示:**
          - 各ファイルの総印刷/加工時間、フィラメント/材料使用量、移動距離などの統計情報を計算し、比較表形式で表示するUIを作成します。
      - [ ] **今回のステップが問題なく実装できてるか確認**
### Step 5.2: テストと最終調整

  * **目的:** 機能全体の安定性と品質を確保します。

  * **タスクリスト:**

      - [ ] **機能単体テストの作成:**
          - CNCファイルの読み込みと描画が正しく行われるかテストします。
          - 3DプリントファイルとCNCファイルを同時に読み込み、表示が崩れないかテストします。
      - [ ] **エラーケースのテスト:**
          - 破損したG-codeファイル、非常に大きなサイズのファイルを読み込ませた際の挙動をテストし、アプリがクラッシュしないことを確認します。
      - [ ] **ユーザビリティテスト:**
          - 一連の操作（ファイル読み込み、レイヤー操作、可視性変更）が直感的に行えるか、第三者の視点で評価します。
      - [ ] **今回のステップが問題なく実装できてるか確認**
-----

## 📊 リスク管理

### 高リスク項目

1.  **`libvgcode`の制限:** 複数Viewerインスタンスの同時利用に深刻なパフォーマンス問題や制約がある場合。
      * **対策:** Phase 1.2での検証を最優先で行う。問題が解決できない場合は、単一のViewerインスタンスに全ての描画データを集約するアーキテクチャへの変更を検討する。
2.  **CNC G-codeの多様性:** CAMソフトウェアによってG-codeの"方言"が異なり、パス抽出がうまく機能しない可能性がある。
      * **対策:** まずは主要なCAM（例: Fusion 360, Vectric）が出力する形式をターゲットとする。サポートするG-code形式をドキュメントに明記し、ユーザーからのフィードバックに基づき対応範囲を拡大する。

### 中リスク項目

1.  **UIの複雑化:** 機能が増えることでUIが煩雑になり、ユーザー体験を損なう。
      * **対策:** 各UI要素をパネルにまとめ、表示/非表示を切り替えられるようにする。ツールチップを積極的に活用し、各機能の説明を行う。
2.  **パフォーマンス低下:** 扱うデータ量が倍増することによるフレームレートの低下。
      * **対策:** Phase 3.2で計画している最適化（インスタンス描画、LOD）を確実に実装する。読み込めるファイル数に上限（例：最初は4つまでなど）を設けることも検討する。

## 2024-06-13
- コミット「41ddd7ad9dcd84cf28663ca2b66bffa78bfcc3a6」へcheckout（作業ディレクトリを過去状態に戻す対応）。
  - 目的：過去の安定状態への復帰、または検証のため。
  - 手順：stashで現状退避→checkout実施。