name: Generate release version of flatpak manifest

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'Tag (e.g. version_1.2.3-rc1)'
        required: true
        type: string

  push:
    tags:
      - '*'

jobs:
  bundle_flatpak:
    uses: Prusa-Development/PrusaSlicer-Actions/.github/workflows/get_release_flatpak_manifest.yml@master
    secrets: inherit
    with:
      actions_branch: master
      tag: ${{ inputs.tag || github.ref_name }}
