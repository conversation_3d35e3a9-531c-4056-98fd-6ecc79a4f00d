### Version
_Version of PrusaSlicer used goes here_

_Use `About->About PrusaSlicer` for release versions_

_For -dev versions, use `git describe --tag` or get the hash value for the version you downloaded or `git rev-parse HEAD`_

### Operating system type + version
_What OS are you using, and state any version #s_
_In case of 3D rendering issues, please attach the content of menu Help -> System Info dialog_

### 3D printer brand / version + firmware version (if known)
_What 3D printer brand / version are you printing on, is it a stock model or did you modify the printer, what firmware is running on your printer, version of the firmware #s_

### Behavior
* _Describe the problem_
* _Steps needed to reproduce the problem_
  * _If this is a command-line slicing issue, include the options used_
* _Expected Results_
* _Actual Results_
  * _Screenshots from __*PrusaSlicer*__ preview are preferred_

_Is this a new feature request?_

#### Project File (.3MF) where problem occurs
_Upload a PrusaSlicer Project File (.3MF) (`Plater -> Export plate as 3MF` for Slic3r PE 1.41.2 and older, `File -> Save` / `Save Project` for PrusaSlicer, Slic3r PE 1.42.0-alpha and newer)_
_Images (PNG, GIF, JPEG), PDFs or text files could be drag & dropped to the issue directly, while all other files need to be zipped first (.zip, .gz)_
