---
description: 
globs: 
alwaysApply: true
---
---
description: "README.md を毎回参照し、日本語で記載する。development_plan.md を毎回更新。ディレクトリ構造を毎回確認し、作業前に作業プランを策定するルール"
globs: ["**/*"]
alwaysApply: true
---

# プロジェクトガイドライン

このルールは、作業開始前および作業中に必ず遵守すべきガイドラインを定めます。

## ✅ チェック項目

1. **README.md を毎回参照**  
   - README.md は日本語で記載してください。  
   - 作業前に内容を確認し、必要に応じて最新の情報を反映してください。

2. **tasks.md の毎回更新**  
   - 開発スケジュールやタスクは常に最新に保つこと。  
   - 作業前後に内容を更新し、プロジェクトの進捗を明確に管理してください。

3. **ディレクトリ構造の定期確認**  
   - 作業を始める際に、プロジェクトのフォルダ構成を確認してください。  
   - ファイルやフォルダが想定通りに組織されているかチェックし、必要があれば再整理してください。

4. **作業前プランの立案**  
   - 実際のコーディングや変更前に「何を」「どの順で」「どう進めるか」を明確化したプランを立ててください。  
   - 作業手順や目的を簡潔に記述し、作業中の迷いを減らしましょう。

5. **テストする際の手順を提示**  
   - 実装してテストをする際に必ず手順を記載してください
   - テストして確認する点や気をつけるべき点を記載してください。

# 段階的実装アプローチ  
重要度に応じて３段階で原則を適用（認知負荷を管理）

## Phase 1：最重要（絶対守る）  
1. **diff表示 → 承認待機 → 実装開始**：diff表示後は必ず「承認をお待ちしてます」と明記し、「OK」「実装して」「良い」「承認」などの明示的合意を得る  
2. **実装後のlint/test実行**：品質担保の最低線  
3. **既存テスト削除の完全禁止**：データ損失防止  
4. **深い思考をする**

## Phase 2：重要（意識的に実行）  
1. **技術判断の記録**：なぜその選択をしたかの3行メモ  
2. **既存コードの事前調査**：同様実装を持つ既存コード3-5ファイルを確認し、命名規則・アーキテクチャパターン・コードスタイルを継承

## Phase 3：理想（余裕がある時）  
1. **パフォーマンス最適化**：可読性・保守性・バグリスクを評価し許容範囲内で実行  
2. **詳細コメント記述**：単純翻訳ではなく技術的意図を説明  

# 深い思考の自動実行（毎回必須）  
回答前に必ず以下を実行：

## #1. 思考トリガー（自問自答）  
- 「なぜ？」を3回自問したか？（Why-Why-Why分析）  
- 「本当にこれで十分か？」と疑ったか？  
- 「ユーザーが本当に求めているのは何か？」を考えたか？  
- 「もっと良い方法はないか？」を30秒考えたか？

## #2. 多角的検討（３つの視点で必須検討）  
- 技術的観点：実装可能性、パフォーマンス、保守性  
- ユーザー体験：使いやすさ、理解しやすさ、期待との一致  
- 運用観点：長期的影響、拡張性、リスク  

## #3. 品質の自己採点（各項目4点以上で合格）  
- 具体性・抽象的でなく実行可能か？（5点満点）
