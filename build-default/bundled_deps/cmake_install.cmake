# Install script for directory: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/dist")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Release")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

# Set default install directory permissions.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/objdump")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for each subdirectory.
  include("/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh/cmake_install.cmake")
  include("/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude/cmake_install.cmake")
  include("/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/miniz/cmake_install.cmake")
  include("/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess/cmake_install.cmake")
  include("/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/agg/cmake_install.cmake")
  include("/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/libigl/cmake_install.cmake")
  include("/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hints/cmake_install.cmake")
  include("/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/libnest2d/cmake_install.cmake")
  include("/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui/cmake_install.cmake")
  include("/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hidapi/cmake_install.cmake")

endif()

