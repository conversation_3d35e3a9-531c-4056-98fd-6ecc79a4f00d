# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/Applications/CMake.app/Contents/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/Applications/CMake.app/Contents/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/Applications/CMake.app/Contents/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude//CMakeFiles/progress.marks
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/avrdude/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/avrdude/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/avrdude/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/avrdude/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
bundled_deps/avrdude/CMakeFiles/avrdude.dir/rule:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/avrdude/CMakeFiles/avrdude.dir/rule
.PHONY : bundled_deps/avrdude/CMakeFiles/avrdude.dir/rule

# Convenience name for target.
avrdude: bundled_deps/avrdude/CMakeFiles/avrdude.dir/rule
.PHONY : avrdude

# fast build rule for target.
avrdude/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/build
.PHONY : avrdude/fast

# Convenience name for target.
bundled_deps/avrdude/CMakeFiles/avrdude-slic3r.dir/rule:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/avrdude/CMakeFiles/avrdude-slic3r.dir/rule
.PHONY : bundled_deps/avrdude/CMakeFiles/avrdude-slic3r.dir/rule

# Convenience name for target.
avrdude-slic3r: bundled_deps/avrdude/CMakeFiles/avrdude-slic3r.dir/rule
.PHONY : avrdude-slic3r

# fast build rule for target.
avrdude-slic3r/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude-slic3r.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude-slic3r.dir/build
.PHONY : avrdude-slic3r/fast

avrdude/arduino.o: avrdude/arduino.c.o
.PHONY : avrdude/arduino.o

# target to build an object file
avrdude/arduino.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/arduino.c.o
.PHONY : avrdude/arduino.c.o

avrdude/arduino.i: avrdude/arduino.c.i
.PHONY : avrdude/arduino.i

# target to preprocess a source file
avrdude/arduino.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/arduino.c.i
.PHONY : avrdude/arduino.c.i

avrdude/arduino.s: avrdude/arduino.c.s
.PHONY : avrdude/arduino.s

# target to generate assembly for a file
avrdude/arduino.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/arduino.c.s
.PHONY : avrdude/arduino.c.s

avrdude/avr.o: avrdude/avr.c.o
.PHONY : avrdude/avr.o

# target to build an object file
avrdude/avr.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr.c.o
.PHONY : avrdude/avr.c.o

avrdude/avr.i: avrdude/avr.c.i
.PHONY : avrdude/avr.i

# target to preprocess a source file
avrdude/avr.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr.c.i
.PHONY : avrdude/avr.c.i

avrdude/avr.s: avrdude/avr.c.s
.PHONY : avrdude/avr.s

# target to generate assembly for a file
avrdude/avr.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr.c.s
.PHONY : avrdude/avr.c.s

avrdude/avr910.o: avrdude/avr910.c.o
.PHONY : avrdude/avr910.o

# target to build an object file
avrdude/avr910.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr910.c.o
.PHONY : avrdude/avr910.c.o

avrdude/avr910.i: avrdude/avr910.c.i
.PHONY : avrdude/avr910.i

# target to preprocess a source file
avrdude/avr910.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr910.c.i
.PHONY : avrdude/avr910.c.i

avrdude/avr910.s: avrdude/avr910.c.s
.PHONY : avrdude/avr910.s

# target to generate assembly for a file
avrdude/avr910.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr910.c.s
.PHONY : avrdude/avr910.c.s

avrdude/avrdude-slic3r.o: avrdude/avrdude-slic3r.cpp.o
.PHONY : avrdude/avrdude-slic3r.o

# target to build an object file
avrdude/avrdude-slic3r.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.o
.PHONY : avrdude/avrdude-slic3r.cpp.o

avrdude/avrdude-slic3r.i: avrdude/avrdude-slic3r.cpp.i
.PHONY : avrdude/avrdude-slic3r.i

# target to preprocess a source file
avrdude/avrdude-slic3r.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.i
.PHONY : avrdude/avrdude-slic3r.cpp.i

avrdude/avrdude-slic3r.s: avrdude/avrdude-slic3r.cpp.s
.PHONY : avrdude/avrdude-slic3r.s

# target to generate assembly for a file
avrdude/avrdude-slic3r.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.s
.PHONY : avrdude/avrdude-slic3r.cpp.s

avrdude/avrpart.o: avrdude/avrpart.c.o
.PHONY : avrdude/avrpart.o

# target to build an object file
avrdude/avrpart.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrpart.c.o
.PHONY : avrdude/avrpart.c.o

avrdude/avrpart.i: avrdude/avrpart.c.i
.PHONY : avrdude/avrpart.i

# target to preprocess a source file
avrdude/avrpart.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrpart.c.i
.PHONY : avrdude/avrpart.c.i

avrdude/avrpart.s: avrdude/avrpart.c.s
.PHONY : avrdude/avrpart.s

# target to generate assembly for a file
avrdude/avrpart.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrpart.c.s
.PHONY : avrdude/avrpart.c.s

avrdude/bitbang.o: avrdude/bitbang.c.o
.PHONY : avrdude/bitbang.o

# target to build an object file
avrdude/bitbang.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/bitbang.c.o
.PHONY : avrdude/bitbang.c.o

avrdude/bitbang.i: avrdude/bitbang.c.i
.PHONY : avrdude/bitbang.i

# target to preprocess a source file
avrdude/bitbang.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/bitbang.c.i
.PHONY : avrdude/bitbang.c.i

avrdude/bitbang.s: avrdude/bitbang.c.s
.PHONY : avrdude/bitbang.s

# target to generate assembly for a file
avrdude/bitbang.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/bitbang.c.s
.PHONY : avrdude/bitbang.c.s

avrdude/buspirate.o: avrdude/buspirate.c.o
.PHONY : avrdude/buspirate.o

# target to build an object file
avrdude/buspirate.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/buspirate.c.o
.PHONY : avrdude/buspirate.c.o

avrdude/buspirate.i: avrdude/buspirate.c.i
.PHONY : avrdude/buspirate.i

# target to preprocess a source file
avrdude/buspirate.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/buspirate.c.i
.PHONY : avrdude/buspirate.c.i

avrdude/buspirate.s: avrdude/buspirate.c.s
.PHONY : avrdude/buspirate.s

# target to generate assembly for a file
avrdude/buspirate.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/buspirate.c.s
.PHONY : avrdude/buspirate.c.s

avrdude/butterfly.o: avrdude/butterfly.c.o
.PHONY : avrdude/butterfly.o

# target to build an object file
avrdude/butterfly.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/butterfly.c.o
.PHONY : avrdude/butterfly.c.o

avrdude/butterfly.i: avrdude/butterfly.c.i
.PHONY : avrdude/butterfly.i

# target to preprocess a source file
avrdude/butterfly.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/butterfly.c.i
.PHONY : avrdude/butterfly.c.i

avrdude/butterfly.s: avrdude/butterfly.c.s
.PHONY : avrdude/butterfly.s

# target to generate assembly for a file
avrdude/butterfly.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/butterfly.c.s
.PHONY : avrdude/butterfly.c.s

avrdude/config.o: avrdude/config.c.o
.PHONY : avrdude/config.o

# target to build an object file
avrdude/config.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config.c.o
.PHONY : avrdude/config.c.o

avrdude/config.i: avrdude/config.c.i
.PHONY : avrdude/config.i

# target to preprocess a source file
avrdude/config.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config.c.i
.PHONY : avrdude/config.c.i

avrdude/config.s: avrdude/config.c.s
.PHONY : avrdude/config.s

# target to generate assembly for a file
avrdude/config.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config.c.s
.PHONY : avrdude/config.c.s

avrdude/config_gram.o: avrdude/config_gram.c.o
.PHONY : avrdude/config_gram.o

# target to build an object file
avrdude/config_gram.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config_gram.c.o
.PHONY : avrdude/config_gram.c.o

avrdude/config_gram.i: avrdude/config_gram.c.i
.PHONY : avrdude/config_gram.i

# target to preprocess a source file
avrdude/config_gram.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config_gram.c.i
.PHONY : avrdude/config_gram.c.i

avrdude/config_gram.s: avrdude/config_gram.c.s
.PHONY : avrdude/config_gram.s

# target to generate assembly for a file
avrdude/config_gram.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config_gram.c.s
.PHONY : avrdude/config_gram.c.s

avrdude/crc16.o: avrdude/crc16.c.o
.PHONY : avrdude/crc16.o

# target to build an object file
avrdude/crc16.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/crc16.c.o
.PHONY : avrdude/crc16.c.o

avrdude/crc16.i: avrdude/crc16.c.i
.PHONY : avrdude/crc16.i

# target to preprocess a source file
avrdude/crc16.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/crc16.c.i
.PHONY : avrdude/crc16.c.i

avrdude/crc16.s: avrdude/crc16.c.s
.PHONY : avrdude/crc16.s

# target to generate assembly for a file
avrdude/crc16.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/crc16.c.s
.PHONY : avrdude/crc16.c.s

avrdude/fileio.o: avrdude/fileio.c.o
.PHONY : avrdude/fileio.o

# target to build an object file
avrdude/fileio.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/fileio.c.o
.PHONY : avrdude/fileio.c.o

avrdude/fileio.i: avrdude/fileio.c.i
.PHONY : avrdude/fileio.i

# target to preprocess a source file
avrdude/fileio.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/fileio.c.i
.PHONY : avrdude/fileio.c.i

avrdude/fileio.s: avrdude/fileio.c.s
.PHONY : avrdude/fileio.s

# target to generate assembly for a file
avrdude/fileio.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/fileio.c.s
.PHONY : avrdude/fileio.c.s

avrdude/lexer.o: avrdude/lexer.c.o
.PHONY : avrdude/lexer.o

# target to build an object file
avrdude/lexer.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lexer.c.o
.PHONY : avrdude/lexer.c.o

avrdude/lexer.i: avrdude/lexer.c.i
.PHONY : avrdude/lexer.i

# target to preprocess a source file
avrdude/lexer.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lexer.c.i
.PHONY : avrdude/lexer.c.i

avrdude/lexer.s: avrdude/lexer.c.s
.PHONY : avrdude/lexer.s

# target to generate assembly for a file
avrdude/lexer.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lexer.c.s
.PHONY : avrdude/lexer.c.s

avrdude/linuxgpio.o: avrdude/linuxgpio.c.o
.PHONY : avrdude/linuxgpio.o

# target to build an object file
avrdude/linuxgpio.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.o
.PHONY : avrdude/linuxgpio.c.o

avrdude/linuxgpio.i: avrdude/linuxgpio.c.i
.PHONY : avrdude/linuxgpio.i

# target to preprocess a source file
avrdude/linuxgpio.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.i
.PHONY : avrdude/linuxgpio.c.i

avrdude/linuxgpio.s: avrdude/linuxgpio.c.s
.PHONY : avrdude/linuxgpio.s

# target to generate assembly for a file
avrdude/linuxgpio.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.s
.PHONY : avrdude/linuxgpio.c.s

avrdude/lists.o: avrdude/lists.c.o
.PHONY : avrdude/lists.o

# target to build an object file
avrdude/lists.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lists.c.o
.PHONY : avrdude/lists.c.o

avrdude/lists.i: avrdude/lists.c.i
.PHONY : avrdude/lists.i

# target to preprocess a source file
avrdude/lists.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lists.c.i
.PHONY : avrdude/lists.c.i

avrdude/lists.s: avrdude/lists.c.s
.PHONY : avrdude/lists.s

# target to generate assembly for a file
avrdude/lists.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lists.c.s
.PHONY : avrdude/lists.c.s

avrdude/main-standalone.o: avrdude/main-standalone.cpp.o
.PHONY : avrdude/main-standalone.o

# target to build an object file
avrdude/main-standalone.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude-slic3r.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude-slic3r.dir/avrdude/main-standalone.cpp.o
.PHONY : avrdude/main-standalone.cpp.o

avrdude/main-standalone.i: avrdude/main-standalone.cpp.i
.PHONY : avrdude/main-standalone.i

# target to preprocess a source file
avrdude/main-standalone.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude-slic3r.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude-slic3r.dir/avrdude/main-standalone.cpp.i
.PHONY : avrdude/main-standalone.cpp.i

avrdude/main-standalone.s: avrdude/main-standalone.cpp.s
.PHONY : avrdude/main-standalone.s

# target to generate assembly for a file
avrdude/main-standalone.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude-slic3r.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude-slic3r.dir/avrdude/main-standalone.cpp.s
.PHONY : avrdude/main-standalone.cpp.s

avrdude/main.o: avrdude/main.c.o
.PHONY : avrdude/main.o

# target to build an object file
avrdude/main.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/main.c.o
.PHONY : avrdude/main.c.o

avrdude/main.i: avrdude/main.c.i
.PHONY : avrdude/main.i

# target to preprocess a source file
avrdude/main.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/main.c.i
.PHONY : avrdude/main.c.i

avrdude/main.s: avrdude/main.c.s
.PHONY : avrdude/main.s

# target to generate assembly for a file
avrdude/main.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/main.c.s
.PHONY : avrdude/main.c.s

avrdude/pgm.o: avrdude/pgm.c.o
.PHONY : avrdude/pgm.o

# target to build an object file
avrdude/pgm.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm.c.o
.PHONY : avrdude/pgm.c.o

avrdude/pgm.i: avrdude/pgm.c.i
.PHONY : avrdude/pgm.i

# target to preprocess a source file
avrdude/pgm.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm.c.i
.PHONY : avrdude/pgm.c.i

avrdude/pgm.s: avrdude/pgm.c.s
.PHONY : avrdude/pgm.s

# target to generate assembly for a file
avrdude/pgm.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm.c.s
.PHONY : avrdude/pgm.c.s

avrdude/pgm_type.o: avrdude/pgm_type.c.o
.PHONY : avrdude/pgm_type.o

# target to build an object file
avrdude/pgm_type.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm_type.c.o
.PHONY : avrdude/pgm_type.c.o

avrdude/pgm_type.i: avrdude/pgm_type.c.i
.PHONY : avrdude/pgm_type.i

# target to preprocess a source file
avrdude/pgm_type.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm_type.c.i
.PHONY : avrdude/pgm_type.c.i

avrdude/pgm_type.s: avrdude/pgm_type.c.s
.PHONY : avrdude/pgm_type.s

# target to generate assembly for a file
avrdude/pgm_type.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm_type.c.s
.PHONY : avrdude/pgm_type.c.s

avrdude/pickit2.o: avrdude/pickit2.c.o
.PHONY : avrdude/pickit2.o

# target to build an object file
avrdude/pickit2.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pickit2.c.o
.PHONY : avrdude/pickit2.c.o

avrdude/pickit2.i: avrdude/pickit2.c.i
.PHONY : avrdude/pickit2.i

# target to preprocess a source file
avrdude/pickit2.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pickit2.c.i
.PHONY : avrdude/pickit2.c.i

avrdude/pickit2.s: avrdude/pickit2.c.s
.PHONY : avrdude/pickit2.s

# target to generate assembly for a file
avrdude/pickit2.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pickit2.c.s
.PHONY : avrdude/pickit2.c.s

avrdude/pindefs.o: avrdude/pindefs.c.o
.PHONY : avrdude/pindefs.o

# target to build an object file
avrdude/pindefs.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pindefs.c.o
.PHONY : avrdude/pindefs.c.o

avrdude/pindefs.i: avrdude/pindefs.c.i
.PHONY : avrdude/pindefs.i

# target to preprocess a source file
avrdude/pindefs.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pindefs.c.i
.PHONY : avrdude/pindefs.c.i

avrdude/pindefs.s: avrdude/pindefs.c.s
.PHONY : avrdude/pindefs.s

# target to generate assembly for a file
avrdude/pindefs.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pindefs.c.s
.PHONY : avrdude/pindefs.c.s

avrdude/safemode.o: avrdude/safemode.c.o
.PHONY : avrdude/safemode.o

# target to build an object file
avrdude/safemode.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/safemode.c.o
.PHONY : avrdude/safemode.c.o

avrdude/safemode.i: avrdude/safemode.c.i
.PHONY : avrdude/safemode.i

# target to preprocess a source file
avrdude/safemode.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/safemode.c.i
.PHONY : avrdude/safemode.c.i

avrdude/safemode.s: avrdude/safemode.c.s
.PHONY : avrdude/safemode.s

# target to generate assembly for a file
avrdude/safemode.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/safemode.c.s
.PHONY : avrdude/safemode.c.s

avrdude/ser_avrdoper.o: avrdude/ser_avrdoper.c.o
.PHONY : avrdude/ser_avrdoper.o

# target to build an object file
avrdude/ser_avrdoper.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.o
.PHONY : avrdude/ser_avrdoper.c.o

avrdude/ser_avrdoper.i: avrdude/ser_avrdoper.c.i
.PHONY : avrdude/ser_avrdoper.i

# target to preprocess a source file
avrdude/ser_avrdoper.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.i
.PHONY : avrdude/ser_avrdoper.c.i

avrdude/ser_avrdoper.s: avrdude/ser_avrdoper.c.s
.PHONY : avrdude/ser_avrdoper.s

# target to generate assembly for a file
avrdude/ser_avrdoper.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.s
.PHONY : avrdude/ser_avrdoper.c.s

avrdude/ser_posix.o: avrdude/ser_posix.c.o
.PHONY : avrdude/ser_posix.o

# target to build an object file
avrdude/ser_posix.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_posix.c.o
.PHONY : avrdude/ser_posix.c.o

avrdude/ser_posix.i: avrdude/ser_posix.c.i
.PHONY : avrdude/ser_posix.i

# target to preprocess a source file
avrdude/ser_posix.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_posix.c.i
.PHONY : avrdude/ser_posix.c.i

avrdude/ser_posix.s: avrdude/ser_posix.c.s
.PHONY : avrdude/ser_posix.s

# target to generate assembly for a file
avrdude/ser_posix.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_posix.c.s
.PHONY : avrdude/ser_posix.c.s

avrdude/ser_win32.o: avrdude/ser_win32.c.o
.PHONY : avrdude/ser_win32.o

# target to build an object file
avrdude/ser_win32.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_win32.c.o
.PHONY : avrdude/ser_win32.c.o

avrdude/ser_win32.i: avrdude/ser_win32.c.i
.PHONY : avrdude/ser_win32.i

# target to preprocess a source file
avrdude/ser_win32.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_win32.c.i
.PHONY : avrdude/ser_win32.c.i

avrdude/ser_win32.s: avrdude/ser_win32.c.s
.PHONY : avrdude/ser_win32.s

# target to generate assembly for a file
avrdude/ser_win32.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_win32.c.s
.PHONY : avrdude/ser_win32.c.s

avrdude/serbb_posix.o: avrdude/serbb_posix.c.o
.PHONY : avrdude/serbb_posix.o

# target to build an object file
avrdude/serbb_posix.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.o
.PHONY : avrdude/serbb_posix.c.o

avrdude/serbb_posix.i: avrdude/serbb_posix.c.i
.PHONY : avrdude/serbb_posix.i

# target to preprocess a source file
avrdude/serbb_posix.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.i
.PHONY : avrdude/serbb_posix.c.i

avrdude/serbb_posix.s: avrdude/serbb_posix.c.s
.PHONY : avrdude/serbb_posix.s

# target to generate assembly for a file
avrdude/serbb_posix.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.s
.PHONY : avrdude/serbb_posix.c.s

avrdude/serbb_win32.o: avrdude/serbb_win32.c.o
.PHONY : avrdude/serbb_win32.o

# target to build an object file
avrdude/serbb_win32.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.o
.PHONY : avrdude/serbb_win32.c.o

avrdude/serbb_win32.i: avrdude/serbb_win32.c.i
.PHONY : avrdude/serbb_win32.i

# target to preprocess a source file
avrdude/serbb_win32.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.i
.PHONY : avrdude/serbb_win32.c.i

avrdude/serbb_win32.s: avrdude/serbb_win32.c.s
.PHONY : avrdude/serbb_win32.s

# target to generate assembly for a file
avrdude/serbb_win32.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.s
.PHONY : avrdude/serbb_win32.c.s

avrdude/stk500.o: avrdude/stk500.c.o
.PHONY : avrdude/stk500.o

# target to build an object file
avrdude/stk500.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500.c.o
.PHONY : avrdude/stk500.c.o

avrdude/stk500.i: avrdude/stk500.c.i
.PHONY : avrdude/stk500.i

# target to preprocess a source file
avrdude/stk500.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500.c.i
.PHONY : avrdude/stk500.c.i

avrdude/stk500.s: avrdude/stk500.c.s
.PHONY : avrdude/stk500.s

# target to generate assembly for a file
avrdude/stk500.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500.c.s
.PHONY : avrdude/stk500.c.s

avrdude/stk500generic.o: avrdude/stk500generic.c.o
.PHONY : avrdude/stk500generic.o

# target to build an object file
avrdude/stk500generic.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500generic.c.o
.PHONY : avrdude/stk500generic.c.o

avrdude/stk500generic.i: avrdude/stk500generic.c.i
.PHONY : avrdude/stk500generic.i

# target to preprocess a source file
avrdude/stk500generic.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500generic.c.i
.PHONY : avrdude/stk500generic.c.i

avrdude/stk500generic.s: avrdude/stk500generic.c.s
.PHONY : avrdude/stk500generic.s

# target to generate assembly for a file
avrdude/stk500generic.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500generic.c.s
.PHONY : avrdude/stk500generic.c.s

avrdude/stk500v2.o: avrdude/stk500v2.c.o
.PHONY : avrdude/stk500v2.o

# target to build an object file
avrdude/stk500v2.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500v2.c.o
.PHONY : avrdude/stk500v2.c.o

avrdude/stk500v2.i: avrdude/stk500v2.c.i
.PHONY : avrdude/stk500v2.i

# target to preprocess a source file
avrdude/stk500v2.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500v2.c.i
.PHONY : avrdude/stk500v2.c.i

avrdude/stk500v2.s: avrdude/stk500v2.c.s
.PHONY : avrdude/stk500v2.s

# target to generate assembly for a file
avrdude/stk500v2.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500v2.c.s
.PHONY : avrdude/stk500v2.c.s

avrdude/term.o: avrdude/term.c.o
.PHONY : avrdude/term.o

# target to build an object file
avrdude/term.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/term.c.o
.PHONY : avrdude/term.c.o

avrdude/term.i: avrdude/term.c.i
.PHONY : avrdude/term.i

# target to preprocess a source file
avrdude/term.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/term.c.i
.PHONY : avrdude/term.c.i

avrdude/term.s: avrdude/term.c.s
.PHONY : avrdude/term.s

# target to generate assembly for a file
avrdude/term.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/term.c.s
.PHONY : avrdude/term.c.s

avrdude/update.o: avrdude/update.c.o
.PHONY : avrdude/update.o

# target to build an object file
avrdude/update.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/update.c.o
.PHONY : avrdude/update.c.o

avrdude/update.i: avrdude/update.c.i
.PHONY : avrdude/update.i

# target to preprocess a source file
avrdude/update.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/update.c.i
.PHONY : avrdude/update.c.i

avrdude/update.s: avrdude/update.c.s
.PHONY : avrdude/update.s

# target to generate assembly for a file
avrdude/update.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/update.c.s
.PHONY : avrdude/update.c.s

avrdude/wiring.o: avrdude/wiring.c.o
.PHONY : avrdude/wiring.o

# target to build an object file
avrdude/wiring.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/wiring.c.o
.PHONY : avrdude/wiring.c.o

avrdude/wiring.i: avrdude/wiring.c.i
.PHONY : avrdude/wiring.i

# target to preprocess a source file
avrdude/wiring.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/wiring.c.i
.PHONY : avrdude/wiring.c.i

avrdude/wiring.s: avrdude/wiring.c.s
.PHONY : avrdude/wiring.s

# target to generate assembly for a file
avrdude/wiring.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/wiring.c.s
.PHONY : avrdude/wiring.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... avrdude"
	@echo "... avrdude-slic3r"
	@echo "... avrdude/arduino.o"
	@echo "... avrdude/arduino.i"
	@echo "... avrdude/arduino.s"
	@echo "... avrdude/avr.o"
	@echo "... avrdude/avr.i"
	@echo "... avrdude/avr.s"
	@echo "... avrdude/avr910.o"
	@echo "... avrdude/avr910.i"
	@echo "... avrdude/avr910.s"
	@echo "... avrdude/avrdude-slic3r.o"
	@echo "... avrdude/avrdude-slic3r.i"
	@echo "... avrdude/avrdude-slic3r.s"
	@echo "... avrdude/avrpart.o"
	@echo "... avrdude/avrpart.i"
	@echo "... avrdude/avrpart.s"
	@echo "... avrdude/bitbang.o"
	@echo "... avrdude/bitbang.i"
	@echo "... avrdude/bitbang.s"
	@echo "... avrdude/buspirate.o"
	@echo "... avrdude/buspirate.i"
	@echo "... avrdude/buspirate.s"
	@echo "... avrdude/butterfly.o"
	@echo "... avrdude/butterfly.i"
	@echo "... avrdude/butterfly.s"
	@echo "... avrdude/config.o"
	@echo "... avrdude/config.i"
	@echo "... avrdude/config.s"
	@echo "... avrdude/config_gram.o"
	@echo "... avrdude/config_gram.i"
	@echo "... avrdude/config_gram.s"
	@echo "... avrdude/crc16.o"
	@echo "... avrdude/crc16.i"
	@echo "... avrdude/crc16.s"
	@echo "... avrdude/fileio.o"
	@echo "... avrdude/fileio.i"
	@echo "... avrdude/fileio.s"
	@echo "... avrdude/lexer.o"
	@echo "... avrdude/lexer.i"
	@echo "... avrdude/lexer.s"
	@echo "... avrdude/linuxgpio.o"
	@echo "... avrdude/linuxgpio.i"
	@echo "... avrdude/linuxgpio.s"
	@echo "... avrdude/lists.o"
	@echo "... avrdude/lists.i"
	@echo "... avrdude/lists.s"
	@echo "... avrdude/main-standalone.o"
	@echo "... avrdude/main-standalone.i"
	@echo "... avrdude/main-standalone.s"
	@echo "... avrdude/main.o"
	@echo "... avrdude/main.i"
	@echo "... avrdude/main.s"
	@echo "... avrdude/pgm.o"
	@echo "... avrdude/pgm.i"
	@echo "... avrdude/pgm.s"
	@echo "... avrdude/pgm_type.o"
	@echo "... avrdude/pgm_type.i"
	@echo "... avrdude/pgm_type.s"
	@echo "... avrdude/pickit2.o"
	@echo "... avrdude/pickit2.i"
	@echo "... avrdude/pickit2.s"
	@echo "... avrdude/pindefs.o"
	@echo "... avrdude/pindefs.i"
	@echo "... avrdude/pindefs.s"
	@echo "... avrdude/safemode.o"
	@echo "... avrdude/safemode.i"
	@echo "... avrdude/safemode.s"
	@echo "... avrdude/ser_avrdoper.o"
	@echo "... avrdude/ser_avrdoper.i"
	@echo "... avrdude/ser_avrdoper.s"
	@echo "... avrdude/ser_posix.o"
	@echo "... avrdude/ser_posix.i"
	@echo "... avrdude/ser_posix.s"
	@echo "... avrdude/ser_win32.o"
	@echo "... avrdude/ser_win32.i"
	@echo "... avrdude/ser_win32.s"
	@echo "... avrdude/serbb_posix.o"
	@echo "... avrdude/serbb_posix.i"
	@echo "... avrdude/serbb_posix.s"
	@echo "... avrdude/serbb_win32.o"
	@echo "... avrdude/serbb_win32.i"
	@echo "... avrdude/serbb_win32.s"
	@echo "... avrdude/stk500.o"
	@echo "... avrdude/stk500.i"
	@echo "... avrdude/stk500.s"
	@echo "... avrdude/stk500generic.o"
	@echo "... avrdude/stk500generic.i"
	@echo "... avrdude/stk500generic.s"
	@echo "... avrdude/stk500v2.o"
	@echo "... avrdude/stk500v2.i"
	@echo "... avrdude/stk500v2.s"
	@echo "... avrdude/term.o"
	@echo "... avrdude/term.i"
	@echo "... avrdude/term.s"
	@echo "... avrdude/update.o"
	@echo "... avrdude/update.i"
	@echo "... avrdude/update.s"
	@echo "... avrdude/wiring.o"
	@echo "... avrdude/wiring.i"
	@echo "... avrdude/wiring.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

