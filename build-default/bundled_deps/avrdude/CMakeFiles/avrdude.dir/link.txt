/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar qc libavrdude.a CMakeFiles/avrdude.dir/avrdude/arduino.c.o CMakeFiles/avrdude.dir/avrdude/avr.c.o CMakeFiles/avrdude.dir/avrdude/avrpart.c.o CMakeFiles/avrdude.dir/avrdude/avr910.c.o CMakeFiles/avrdude.dir/avrdude/bitbang.c.o CMakeFiles/avrdude.dir/avrdude/buspirate.c.o CMakeFiles/avrdude.dir/avrdude/butterfly.c.o CMakeFiles/avrdude.dir/avrdude/config.c.o CMakeFiles/avrdude.dir/avrdude/config_gram.c.o CMakeFiles/avrdude.dir/avrdude/crc16.c.o CMakeFiles/avrdude.dir/avrdude/fileio.c.o CMakeFiles/avrdude.dir/avrdude/lexer.c.o CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.o CMakeFiles/avrdude.dir/avrdude/lists.c.o CMakeFiles/avrdude.dir/avrdude/pgm.c.o CMakeFiles/avrdude.dir/avrdude/pgm_type.c.o CMakeFiles/avrdude.dir/avrdude/pickit2.c.o CMakeFiles/avrdude.dir/avrdude/pindefs.c.o CMakeFiles/avrdude.dir/avrdude/safemode.c.o CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.o CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.o CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.o CMakeFiles/avrdude.dir/avrdude/ser_posix.c.o CMakeFiles/avrdude.dir/avrdude/ser_win32.c.o CMakeFiles/avrdude.dir/avrdude/stk500.c.o CMakeFiles/avrdude.dir/avrdude/stk500generic.c.o CMakeFiles/avrdude.dir/avrdude/stk500v2.c.o CMakeFiles/avrdude.dir/avrdude/term.c.o CMakeFiles/avrdude.dir/avrdude/update.c.o CMakeFiles/avrdude.dir/avrdude/wiring.c.o CMakeFiles/avrdude.dir/avrdude/main.c.o "CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.o"
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib libavrdude.a
