# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# compile C with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc
# compile CXX with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++
C_DEFINES = -DSLIC3R_DESKTOP_INTEGRATION -DSLIC3R_GUI -DWXINTL_NO_GETTEXT_MACRO -D_BSD_SOURCE -D_DEFAULT_SOURCE -DwxNO_UNSAFE_WXSTRING_CONV -DwxUSE_UNICODE

C_INCLUDES = -I/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/platform -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/. -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/localesutils -isystem /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude

C_FLAGSarm64 =  -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -std=gnu99 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fsigned-char -Werror=return-type -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-misleading-indentation

C_FLAGS =  -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -std=gnu99 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fsigned-char -Werror=return-type -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-misleading-indentation

CXX_DEFINES = -DSLIC3R_DESKTOP_INTEGRATION -DSLIC3R_GUI -DWXINTL_NO_GETTEXT_MACRO -D_BSD_SOURCE -D_DEFAULT_SOURCE -DwxNO_UNSAFE_WXSTRING_CONV -DwxUSE_UNICODE

CXX_INCLUDES = -I/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/platform -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/. -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/localesutils -isystem /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude

CXX_FLAGSarm64 =  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fsigned-char -Werror=return-type -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-misleading-indentation

CXX_FLAGS =  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fsigned-char -Werror=return-type -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-misleading-indentation

