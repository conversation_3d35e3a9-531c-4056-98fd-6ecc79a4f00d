
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/arduino.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/arduino.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/arduino.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avr.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avr910.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr910.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr910.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avrpart.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrpart.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrpart.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/bitbang.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/bitbang.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/bitbang.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/buspirate.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/buspirate.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/buspirate.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/butterfly.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/butterfly.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/butterfly.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/config.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/config_gram.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config_gram.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config_gram.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/crc16.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/crc16.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/crc16.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/fileio.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/fileio.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/fileio.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/lexer.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lexer.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lexer.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/linuxgpio.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/lists.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lists.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lists.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/main.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/main.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/main.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pgm.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pgm_type.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm_type.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm_type.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pickit2.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pickit2.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pickit2.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pindefs.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pindefs.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pindefs.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/safemode.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/safemode.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/safemode.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/ser_avrdoper.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/ser_posix.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_posix.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_posix.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/ser_win32.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_win32.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_win32.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/serbb_posix.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/serbb_win32.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/stk500.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/stk500generic.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500generic.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500generic.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/stk500v2.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500v2.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500v2.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/term.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/term.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/term.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/update.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/update.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/update.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/wiring.c" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/wiring.c.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/wiring.c.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avrdude-slic3r.cpp" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.o" "gcc" "bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
