# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Include any dependencies generated for this target.
include bundled_deps/avrdude/CMakeFiles/avrdude.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.make

# Include the progress variables for this target.
include bundled_deps/avrdude/CMakeFiles/avrdude.dir/progress.make

# Include the compile flags for this target's objects.
include bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/arduino.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/arduino.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/arduino.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/arduino.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/arduino.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/arduino.c.o -MF CMakeFiles/avrdude.dir/avrdude/arduino.c.o.d -o CMakeFiles/avrdude.dir/avrdude/arduino.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/arduino.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/arduino.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/arduino.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/arduino.c > CMakeFiles/avrdude.dir/avrdude/arduino.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/arduino.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/arduino.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/arduino.c -o CMakeFiles/avrdude.dir/avrdude/arduino.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avr.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr.c.o -MF CMakeFiles/avrdude.dir/avrdude/avr.c.o.d -o CMakeFiles/avrdude.dir/avrdude/avr.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avr.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/avr.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avr.c > CMakeFiles/avrdude.dir/avrdude/avr.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/avr.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avr.c -o CMakeFiles/avrdude.dir/avrdude/avr.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrpart.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrpart.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avrpart.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrpart.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrpart.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrpart.c.o -MF CMakeFiles/avrdude.dir/avrdude/avrpart.c.o.d -o CMakeFiles/avrdude.dir/avrdude/avrpart.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avrpart.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrpart.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/avrpart.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avrpart.c > CMakeFiles/avrdude.dir/avrdude/avrpart.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrpart.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/avrpart.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avrpart.c -o CMakeFiles/avrdude.dir/avrdude/avrpart.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr910.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr910.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avr910.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr910.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr910.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr910.c.o -MF CMakeFiles/avrdude.dir/avrdude/avr910.c.o.d -o CMakeFiles/avrdude.dir/avrdude/avr910.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avr910.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr910.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/avr910.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avr910.c > CMakeFiles/avrdude.dir/avrdude/avr910.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr910.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/avr910.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avr910.c -o CMakeFiles/avrdude.dir/avrdude/avr910.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/bitbang.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/bitbang.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/bitbang.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/bitbang.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/bitbang.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/bitbang.c.o -MF CMakeFiles/avrdude.dir/avrdude/bitbang.c.o.d -o CMakeFiles/avrdude.dir/avrdude/bitbang.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/bitbang.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/bitbang.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/bitbang.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/bitbang.c > CMakeFiles/avrdude.dir/avrdude/bitbang.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/bitbang.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/bitbang.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/bitbang.c -o CMakeFiles/avrdude.dir/avrdude/bitbang.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/buspirate.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/buspirate.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/buspirate.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/buspirate.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/buspirate.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/buspirate.c.o -MF CMakeFiles/avrdude.dir/avrdude/buspirate.c.o.d -o CMakeFiles/avrdude.dir/avrdude/buspirate.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/buspirate.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/buspirate.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/buspirate.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/buspirate.c > CMakeFiles/avrdude.dir/avrdude/buspirate.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/buspirate.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/buspirate.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/buspirate.c -o CMakeFiles/avrdude.dir/avrdude/buspirate.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/butterfly.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/butterfly.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/butterfly.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/butterfly.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/butterfly.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/butterfly.c.o -MF CMakeFiles/avrdude.dir/avrdude/butterfly.c.o.d -o CMakeFiles/avrdude.dir/avrdude/butterfly.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/butterfly.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/butterfly.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/butterfly.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/butterfly.c > CMakeFiles/avrdude.dir/avrdude/butterfly.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/butterfly.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/butterfly.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/butterfly.c -o CMakeFiles/avrdude.dir/avrdude/butterfly.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/config.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config.c.o -MF CMakeFiles/avrdude.dir/avrdude/config.c.o.d -o CMakeFiles/avrdude.dir/avrdude/config.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/config.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/config.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/config.c > CMakeFiles/avrdude.dir/avrdude/config.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/config.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/config.c -o CMakeFiles/avrdude.dir/avrdude/config.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config_gram.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config_gram.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/config_gram.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config_gram.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config_gram.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config_gram.c.o -MF CMakeFiles/avrdude.dir/avrdude/config_gram.c.o.d -o CMakeFiles/avrdude.dir/avrdude/config_gram.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/config_gram.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config_gram.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/config_gram.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/config_gram.c > CMakeFiles/avrdude.dir/avrdude/config_gram.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config_gram.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/config_gram.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/config_gram.c -o CMakeFiles/avrdude.dir/avrdude/config_gram.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/crc16.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/crc16.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/crc16.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/crc16.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/crc16.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/crc16.c.o -MF CMakeFiles/avrdude.dir/avrdude/crc16.c.o.d -o CMakeFiles/avrdude.dir/avrdude/crc16.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/crc16.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/crc16.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/crc16.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/crc16.c > CMakeFiles/avrdude.dir/avrdude/crc16.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/crc16.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/crc16.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/crc16.c -o CMakeFiles/avrdude.dir/avrdude/crc16.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/fileio.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/fileio.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/fileio.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/fileio.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/fileio.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/fileio.c.o -MF CMakeFiles/avrdude.dir/avrdude/fileio.c.o.d -o CMakeFiles/avrdude.dir/avrdude/fileio.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/fileio.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/fileio.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/fileio.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/fileio.c > CMakeFiles/avrdude.dir/avrdude/fileio.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/fileio.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/fileio.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/fileio.c -o CMakeFiles/avrdude.dir/avrdude/fileio.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lexer.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lexer.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/lexer.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lexer.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lexer.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lexer.c.o -MF CMakeFiles/avrdude.dir/avrdude/lexer.c.o.d -o CMakeFiles/avrdude.dir/avrdude/lexer.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/lexer.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lexer.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/lexer.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/lexer.c > CMakeFiles/avrdude.dir/avrdude/lexer.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lexer.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/lexer.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/lexer.c -o CMakeFiles/avrdude.dir/avrdude/lexer.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/linuxgpio.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.o -MF CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.o.d -o CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/linuxgpio.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/linuxgpio.c > CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/linuxgpio.c -o CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lists.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lists.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/lists.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lists.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lists.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lists.c.o -MF CMakeFiles/avrdude.dir/avrdude/lists.c.o.d -o CMakeFiles/avrdude.dir/avrdude/lists.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/lists.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lists.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/lists.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/lists.c > CMakeFiles/avrdude.dir/avrdude/lists.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lists.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/lists.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/lists.c -o CMakeFiles/avrdude.dir/avrdude/lists.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pgm.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm.c.o -MF CMakeFiles/avrdude.dir/avrdude/pgm.c.o.d -o CMakeFiles/avrdude.dir/avrdude/pgm.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pgm.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/pgm.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pgm.c > CMakeFiles/avrdude.dir/avrdude/pgm.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/pgm.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pgm.c -o CMakeFiles/avrdude.dir/avrdude/pgm.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm_type.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm_type.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pgm_type.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm_type.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm_type.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm_type.c.o -MF CMakeFiles/avrdude.dir/avrdude/pgm_type.c.o.d -o CMakeFiles/avrdude.dir/avrdude/pgm_type.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pgm_type.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm_type.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/pgm_type.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pgm_type.c > CMakeFiles/avrdude.dir/avrdude/pgm_type.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm_type.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/pgm_type.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pgm_type.c -o CMakeFiles/avrdude.dir/avrdude/pgm_type.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pickit2.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pickit2.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pickit2.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pickit2.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pickit2.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pickit2.c.o -MF CMakeFiles/avrdude.dir/avrdude/pickit2.c.o.d -o CMakeFiles/avrdude.dir/avrdude/pickit2.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pickit2.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pickit2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/pickit2.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pickit2.c > CMakeFiles/avrdude.dir/avrdude/pickit2.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pickit2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/pickit2.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pickit2.c -o CMakeFiles/avrdude.dir/avrdude/pickit2.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pindefs.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pindefs.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pindefs.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pindefs.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pindefs.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pindefs.c.o -MF CMakeFiles/avrdude.dir/avrdude/pindefs.c.o.d -o CMakeFiles/avrdude.dir/avrdude/pindefs.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pindefs.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pindefs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/pindefs.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pindefs.c > CMakeFiles/avrdude.dir/avrdude/pindefs.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pindefs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/pindefs.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/pindefs.c -o CMakeFiles/avrdude.dir/avrdude/pindefs.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/safemode.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/safemode.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/safemode.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/safemode.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/safemode.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/safemode.c.o -MF CMakeFiles/avrdude.dir/avrdude/safemode.c.o.d -o CMakeFiles/avrdude.dir/avrdude/safemode.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/safemode.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/safemode.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/safemode.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/safemode.c > CMakeFiles/avrdude.dir/avrdude/safemode.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/safemode.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/safemode.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/safemode.c -o CMakeFiles/avrdude.dir/avrdude/safemode.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/ser_avrdoper.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.o -MF CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.o.d -o CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/ser_avrdoper.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/ser_avrdoper.c > CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/ser_avrdoper.c -o CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/serbb_posix.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.o -MF CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.o.d -o CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/serbb_posix.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/serbb_posix.c > CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/serbb_posix.c -o CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/serbb_win32.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.o -MF CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.o.d -o CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/serbb_win32.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/serbb_win32.c > CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/serbb_win32.c -o CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_posix.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_posix.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/ser_posix.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_posix.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_posix.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_posix.c.o -MF CMakeFiles/avrdude.dir/avrdude/ser_posix.c.o.d -o CMakeFiles/avrdude.dir/avrdude/ser_posix.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/ser_posix.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_posix.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/ser_posix.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/ser_posix.c > CMakeFiles/avrdude.dir/avrdude/ser_posix.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_posix.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/ser_posix.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/ser_posix.c -o CMakeFiles/avrdude.dir/avrdude/ser_posix.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_win32.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_win32.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/ser_win32.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_win32.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_win32.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_win32.c.o -MF CMakeFiles/avrdude.dir/avrdude/ser_win32.c.o.d -o CMakeFiles/avrdude.dir/avrdude/ser_win32.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/ser_win32.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_win32.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/ser_win32.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/ser_win32.c > CMakeFiles/avrdude.dir/avrdude/ser_win32.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_win32.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/ser_win32.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/ser_win32.c -o CMakeFiles/avrdude.dir/avrdude/ser_win32.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/stk500.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500.c.o -MF CMakeFiles/avrdude.dir/avrdude/stk500.c.o.d -o CMakeFiles/avrdude.dir/avrdude/stk500.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/stk500.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/stk500.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/stk500.c > CMakeFiles/avrdude.dir/avrdude/stk500.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/stk500.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/stk500.c -o CMakeFiles/avrdude.dir/avrdude/stk500.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500generic.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500generic.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/stk500generic.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500generic.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500generic.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500generic.c.o -MF CMakeFiles/avrdude.dir/avrdude/stk500generic.c.o.d -o CMakeFiles/avrdude.dir/avrdude/stk500generic.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/stk500generic.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500generic.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/stk500generic.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/stk500generic.c > CMakeFiles/avrdude.dir/avrdude/stk500generic.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500generic.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/stk500generic.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/stk500generic.c -o CMakeFiles/avrdude.dir/avrdude/stk500generic.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500v2.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500v2.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/stk500v2.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500v2.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500v2.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500v2.c.o -MF CMakeFiles/avrdude.dir/avrdude/stk500v2.c.o.d -o CMakeFiles/avrdude.dir/avrdude/stk500v2.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/stk500v2.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500v2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/stk500v2.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/stk500v2.c > CMakeFiles/avrdude.dir/avrdude/stk500v2.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500v2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/stk500v2.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/stk500v2.c -o CMakeFiles/avrdude.dir/avrdude/stk500v2.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/term.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/term.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/term.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/term.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/term.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/term.c.o -MF CMakeFiles/avrdude.dir/avrdude/term.c.o.d -o CMakeFiles/avrdude.dir/avrdude/term.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/term.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/term.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/term.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/term.c > CMakeFiles/avrdude.dir/avrdude/term.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/term.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/term.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/term.c -o CMakeFiles/avrdude.dir/avrdude/term.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/update.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/update.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/update.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/update.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/update.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/update.c.o -MF CMakeFiles/avrdude.dir/avrdude/update.c.o.d -o CMakeFiles/avrdude.dir/avrdude/update.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/update.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/update.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/update.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/update.c > CMakeFiles/avrdude.dir/avrdude/update.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/update.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/update.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/update.c -o CMakeFiles/avrdude.dir/avrdude/update.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/wiring.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/wiring.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/wiring.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/wiring.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/wiring.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/wiring.c.o -MF CMakeFiles/avrdude.dir/avrdude/wiring.c.o.d -o CMakeFiles/avrdude.dir/avrdude/wiring.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/wiring.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/wiring.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/wiring.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/wiring.c > CMakeFiles/avrdude.dir/avrdude/wiring.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/wiring.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/wiring.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/wiring.c -o CMakeFiles/avrdude.dir/avrdude/wiring.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/main.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/main.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/main.c
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/main.c.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building C object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/main.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/main.c.o -MF CMakeFiles/avrdude.dir/avrdude/main.c.o.d -o CMakeFiles/avrdude.dir/avrdude/main.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/main.c

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/avrdude.dir/avrdude/main.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/main.c > CMakeFiles/avrdude.dir/avrdude/main.c.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/avrdude.dir/avrdude/main.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/main.c -o CMakeFiles/avrdude.dir/avrdude/main.c.s

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/flags.make
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avrdude-slic3r.cpp
bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.o: bundled_deps/avrdude/CMakeFiles/avrdude.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building CXX object bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.o -MF CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.o.d -o CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avrdude-slic3r.cpp

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avrdude-slic3r.cpp > CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.i

bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude/avrdude/avrdude-slic3r.cpp -o CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.s

# Object files for target avrdude
avrdude_OBJECTS = \
"CMakeFiles/avrdude.dir/avrdude/arduino.c.o" \
"CMakeFiles/avrdude.dir/avrdude/avr.c.o" \
"CMakeFiles/avrdude.dir/avrdude/avrpart.c.o" \
"CMakeFiles/avrdude.dir/avrdude/avr910.c.o" \
"CMakeFiles/avrdude.dir/avrdude/bitbang.c.o" \
"CMakeFiles/avrdude.dir/avrdude/buspirate.c.o" \
"CMakeFiles/avrdude.dir/avrdude/butterfly.c.o" \
"CMakeFiles/avrdude.dir/avrdude/config.c.o" \
"CMakeFiles/avrdude.dir/avrdude/config_gram.c.o" \
"CMakeFiles/avrdude.dir/avrdude/crc16.c.o" \
"CMakeFiles/avrdude.dir/avrdude/fileio.c.o" \
"CMakeFiles/avrdude.dir/avrdude/lexer.c.o" \
"CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.o" \
"CMakeFiles/avrdude.dir/avrdude/lists.c.o" \
"CMakeFiles/avrdude.dir/avrdude/pgm.c.o" \
"CMakeFiles/avrdude.dir/avrdude/pgm_type.c.o" \
"CMakeFiles/avrdude.dir/avrdude/pickit2.c.o" \
"CMakeFiles/avrdude.dir/avrdude/pindefs.c.o" \
"CMakeFiles/avrdude.dir/avrdude/safemode.c.o" \
"CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.o" \
"CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.o" \
"CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.o" \
"CMakeFiles/avrdude.dir/avrdude/ser_posix.c.o" \
"CMakeFiles/avrdude.dir/avrdude/ser_win32.c.o" \
"CMakeFiles/avrdude.dir/avrdude/stk500.c.o" \
"CMakeFiles/avrdude.dir/avrdude/stk500generic.c.o" \
"CMakeFiles/avrdude.dir/avrdude/stk500v2.c.o" \
"CMakeFiles/avrdude.dir/avrdude/term.c.o" \
"CMakeFiles/avrdude.dir/avrdude/update.c.o" \
"CMakeFiles/avrdude.dir/avrdude/wiring.c.o" \
"CMakeFiles/avrdude.dir/avrdude/main.c.o" \
"CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.o"

# External object files for target avrdude
avrdude_EXTERNAL_OBJECTS =

bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/arduino.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrpart.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avr910.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/bitbang.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/buspirate.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/butterfly.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/config_gram.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/crc16.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/fileio.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lexer.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/linuxgpio.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/lists.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pgm_type.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pickit2.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/pindefs.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/safemode.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_avrdoper.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_posix.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/serbb_win32.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_posix.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/ser_win32.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500generic.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/stk500v2.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/term.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/update.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/wiring.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/main.c.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/avrdude/avrdude-slic3r.cpp.o
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/build.make
bundled_deps/avrdude/libavrdude.a: bundled_deps/avrdude/CMakeFiles/avrdude.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Linking CXX static library libavrdude.a"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && $(CMAKE_COMMAND) -P CMakeFiles/avrdude.dir/cmake_clean_target.cmake
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/avrdude.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
bundled_deps/avrdude/CMakeFiles/avrdude.dir/build: bundled_deps/avrdude/libavrdude.a
.PHONY : bundled_deps/avrdude/CMakeFiles/avrdude.dir/build

bundled_deps/avrdude/CMakeFiles/avrdude.dir/clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude && $(CMAKE_COMMAND) -P CMakeFiles/avrdude.dir/cmake_clean.cmake
.PHONY : bundled_deps/avrdude/CMakeFiles/avrdude.dir/clean

bundled_deps/avrdude/CMakeFiles/avrdude.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/avrdude /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/avrdude/CMakeFiles/avrdude.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : bundled_deps/avrdude/CMakeFiles/avrdude.dir/depend

