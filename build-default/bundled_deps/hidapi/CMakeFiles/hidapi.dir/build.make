# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Include any dependencies generated for this target.
include bundled_deps/hidapi/CMakeFiles/hidapi.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include bundled_deps/hidapi/CMakeFiles/hidapi.dir/compiler_depend.make

# Include the progress variables for this target.
include bundled_deps/hidapi/CMakeFiles/hidapi.dir/progress.make

# Include the compile flags for this target's objects.
include bundled_deps/hidapi/CMakeFiles/hidapi.dir/flags.make

bundled_deps/hidapi/CMakeFiles/hidapi.dir/mac/hid.c.o: bundled_deps/hidapi/CMakeFiles/hidapi.dir/flags.make
bundled_deps/hidapi/CMakeFiles/hidapi.dir/mac/hid.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/hidapi/mac/hid.c
bundled_deps/hidapi/CMakeFiles/hidapi.dir/mac/hid.c.o: bundled_deps/hidapi/CMakeFiles/hidapi.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object bundled_deps/hidapi/CMakeFiles/hidapi.dir/mac/hid.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hidapi && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/hidapi/CMakeFiles/hidapi.dir/mac/hid.c.o -MF CMakeFiles/hidapi.dir/mac/hid.c.o.d -o CMakeFiles/hidapi.dir/mac/hid.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/hidapi/mac/hid.c

bundled_deps/hidapi/CMakeFiles/hidapi.dir/mac/hid.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/hidapi.dir/mac/hid.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hidapi && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/hidapi/mac/hid.c > CMakeFiles/hidapi.dir/mac/hid.c.i

bundled_deps/hidapi/CMakeFiles/hidapi.dir/mac/hid.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/hidapi.dir/mac/hid.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hidapi && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/hidapi/mac/hid.c -o CMakeFiles/hidapi.dir/mac/hid.c.s

# Object files for target hidapi
hidapi_OBJECTS = \
"CMakeFiles/hidapi.dir/mac/hid.c.o"

# External object files for target hidapi
hidapi_EXTERNAL_OBJECTS =

bundled_deps/hidapi/libhidapi.a: bundled_deps/hidapi/CMakeFiles/hidapi.dir/mac/hid.c.o
bundled_deps/hidapi/libhidapi.a: bundled_deps/hidapi/CMakeFiles/hidapi.dir/build.make
bundled_deps/hidapi/libhidapi.a: bundled_deps/hidapi/CMakeFiles/hidapi.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C static library libhidapi.a"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hidapi && $(CMAKE_COMMAND) -P CMakeFiles/hidapi.dir/cmake_clean_target.cmake
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hidapi && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/hidapi.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
bundled_deps/hidapi/CMakeFiles/hidapi.dir/build: bundled_deps/hidapi/libhidapi.a
.PHONY : bundled_deps/hidapi/CMakeFiles/hidapi.dir/build

bundled_deps/hidapi/CMakeFiles/hidapi.dir/clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hidapi && $(CMAKE_COMMAND) -P CMakeFiles/hidapi.dir/cmake_clean.cmake
.PHONY : bundled_deps/hidapi/CMakeFiles/hidapi.dir/clean

bundled_deps/hidapi/CMakeFiles/hidapi.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/hidapi /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hidapi /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hidapi/CMakeFiles/hidapi.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : bundled_deps/hidapi/CMakeFiles/hidapi.dir/depend

