# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/Applications/CMake.app/Contents/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/Applications/CMake.app/Contents/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/Applications/CMake.app/Contents/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess//CMakeFiles/progress.marks
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/glu-libtess/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/glu-libtess/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/glu-libtess/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/glu-libtess/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/rule:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/rule
.PHONY : bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/rule

# Convenience name for target.
glu-libtess: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/rule
.PHONY : glu-libtess

# fast build rule for target.
glu-libtess/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build
.PHONY : glu-libtess/fast

src/dict.o: src/dict.c.o
.PHONY : src/dict.o

# target to build an object file
src/dict.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/dict.c.o
.PHONY : src/dict.c.o

src/dict.i: src/dict.c.i
.PHONY : src/dict.i

# target to preprocess a source file
src/dict.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/dict.c.i
.PHONY : src/dict.c.i

src/dict.s: src/dict.c.s
.PHONY : src/dict.s

# target to generate assembly for a file
src/dict.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/dict.c.s
.PHONY : src/dict.c.s

src/geom.o: src/geom.c.o
.PHONY : src/geom.o

# target to build an object file
src/geom.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/geom.c.o
.PHONY : src/geom.c.o

src/geom.i: src/geom.c.i
.PHONY : src/geom.i

# target to preprocess a source file
src/geom.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/geom.c.i
.PHONY : src/geom.c.i

src/geom.s: src/geom.c.s
.PHONY : src/geom.s

# target to generate assembly for a file
src/geom.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/geom.c.s
.PHONY : src/geom.c.s

src/memalloc.o: src/memalloc.c.o
.PHONY : src/memalloc.o

# target to build an object file
src/memalloc.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/memalloc.c.o
.PHONY : src/memalloc.c.o

src/memalloc.i: src/memalloc.c.i
.PHONY : src/memalloc.i

# target to preprocess a source file
src/memalloc.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/memalloc.c.i
.PHONY : src/memalloc.c.i

src/memalloc.s: src/memalloc.c.s
.PHONY : src/memalloc.s

# target to generate assembly for a file
src/memalloc.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/memalloc.c.s
.PHONY : src/memalloc.c.s

src/mesh.o: src/mesh.c.o
.PHONY : src/mesh.o

# target to build an object file
src/mesh.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/mesh.c.o
.PHONY : src/mesh.c.o

src/mesh.i: src/mesh.c.i
.PHONY : src/mesh.i

# target to preprocess a source file
src/mesh.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/mesh.c.i
.PHONY : src/mesh.c.i

src/mesh.s: src/mesh.c.s
.PHONY : src/mesh.s

# target to generate assembly for a file
src/mesh.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/mesh.c.s
.PHONY : src/mesh.c.s

src/normal.o: src/normal.c.o
.PHONY : src/normal.o

# target to build an object file
src/normal.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/normal.c.o
.PHONY : src/normal.c.o

src/normal.i: src/normal.c.i
.PHONY : src/normal.i

# target to preprocess a source file
src/normal.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/normal.c.i
.PHONY : src/normal.c.i

src/normal.s: src/normal.c.s
.PHONY : src/normal.s

# target to generate assembly for a file
src/normal.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/normal.c.s
.PHONY : src/normal.c.s

src/priorityq.o: src/priorityq.c.o
.PHONY : src/priorityq.o

# target to build an object file
src/priorityq.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/priorityq.c.o
.PHONY : src/priorityq.c.o

src/priorityq.i: src/priorityq.c.i
.PHONY : src/priorityq.i

# target to preprocess a source file
src/priorityq.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/priorityq.c.i
.PHONY : src/priorityq.c.i

src/priorityq.s: src/priorityq.c.s
.PHONY : src/priorityq.s

# target to generate assembly for a file
src/priorityq.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/priorityq.c.s
.PHONY : src/priorityq.c.s

src/render.o: src/render.c.o
.PHONY : src/render.o

# target to build an object file
src/render.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/render.c.o
.PHONY : src/render.c.o

src/render.i: src/render.c.i
.PHONY : src/render.i

# target to preprocess a source file
src/render.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/render.c.i
.PHONY : src/render.c.i

src/render.s: src/render.c.s
.PHONY : src/render.s

# target to generate assembly for a file
src/render.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/render.c.s
.PHONY : src/render.c.s

src/sweep.o: src/sweep.c.o
.PHONY : src/sweep.o

# target to build an object file
src/sweep.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/sweep.c.o
.PHONY : src/sweep.c.o

src/sweep.i: src/sweep.c.i
.PHONY : src/sweep.i

# target to preprocess a source file
src/sweep.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/sweep.c.i
.PHONY : src/sweep.c.i

src/sweep.s: src/sweep.c.s
.PHONY : src/sweep.s

# target to generate assembly for a file
src/sweep.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/sweep.c.s
.PHONY : src/sweep.c.s

src/tess.o: src/tess.c.o
.PHONY : src/tess.o

# target to build an object file
src/tess.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tess.c.o
.PHONY : src/tess.c.o

src/tess.i: src/tess.c.i
.PHONY : src/tess.i

# target to preprocess a source file
src/tess.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tess.c.i
.PHONY : src/tess.c.i

src/tess.s: src/tess.c.s
.PHONY : src/tess.s

# target to generate assembly for a file
src/tess.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tess.c.s
.PHONY : src/tess.c.s

src/tessmono.o: src/tessmono.c.o
.PHONY : src/tessmono.o

# target to build an object file
src/tessmono.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tessmono.c.o
.PHONY : src/tessmono.c.o

src/tessmono.i: src/tessmono.c.i
.PHONY : src/tessmono.i

# target to preprocess a source file
src/tessmono.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tessmono.c.i
.PHONY : src/tessmono.c.i

src/tessmono.s: src/tessmono.c.s
.PHONY : src/tessmono.s

# target to generate assembly for a file
src/tessmono.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tessmono.c.s
.PHONY : src/tessmono.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... glu-libtess"
	@echo "... src/dict.o"
	@echo "... src/dict.i"
	@echo "... src/dict.s"
	@echo "... src/geom.o"
	@echo "... src/geom.i"
	@echo "... src/geom.s"
	@echo "... src/memalloc.o"
	@echo "... src/memalloc.i"
	@echo "... src/memalloc.s"
	@echo "... src/mesh.o"
	@echo "... src/mesh.i"
	@echo "... src/mesh.s"
	@echo "... src/normal.o"
	@echo "... src/normal.i"
	@echo "... src/normal.s"
	@echo "... src/priorityq.o"
	@echo "... src/priorityq.i"
	@echo "... src/priorityq.s"
	@echo "... src/render.o"
	@echo "... src/render.i"
	@echo "... src/render.s"
	@echo "... src/sweep.o"
	@echo "... src/sweep.i"
	@echo "... src/sweep.s"
	@echo "... src/tess.o"
	@echo "... src/tess.i"
	@echo "... src/tess.s"
	@echo "... src/tessmono.o"
	@echo "... src/tessmono.i"
	@echo "... src/tessmono.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

