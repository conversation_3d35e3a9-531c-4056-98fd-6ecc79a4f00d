# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Include any dependencies generated for this target.
include bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/compiler_depend.make

# Include the progress variables for this target.
include bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/progress.make

# Include the compile flags for this target's objects.
include bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/flags.make

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/dict.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/flags.make
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/dict.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/dict.c
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/dict.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/dict.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/dict.c.o -MF CMakeFiles/glu-libtess.dir/src/dict.c.o.d -o CMakeFiles/glu-libtess.dir/src/dict.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/dict.c

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/dict.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/glu-libtess.dir/src/dict.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/dict.c > CMakeFiles/glu-libtess.dir/src/dict.c.i

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/dict.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/glu-libtess.dir/src/dict.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/dict.c -o CMakeFiles/glu-libtess.dir/src/dict.c.s

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/geom.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/flags.make
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/geom.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/geom.c
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/geom.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/geom.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/geom.c.o -MF CMakeFiles/glu-libtess.dir/src/geom.c.o.d -o CMakeFiles/glu-libtess.dir/src/geom.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/geom.c

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/geom.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/glu-libtess.dir/src/geom.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/geom.c > CMakeFiles/glu-libtess.dir/src/geom.c.i

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/geom.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/glu-libtess.dir/src/geom.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/geom.c -o CMakeFiles/glu-libtess.dir/src/geom.c.s

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/memalloc.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/flags.make
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/memalloc.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/memalloc.c
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/memalloc.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/memalloc.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/memalloc.c.o -MF CMakeFiles/glu-libtess.dir/src/memalloc.c.o.d -o CMakeFiles/glu-libtess.dir/src/memalloc.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/memalloc.c

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/memalloc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/glu-libtess.dir/src/memalloc.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/memalloc.c > CMakeFiles/glu-libtess.dir/src/memalloc.c.i

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/memalloc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/glu-libtess.dir/src/memalloc.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/memalloc.c -o CMakeFiles/glu-libtess.dir/src/memalloc.c.s

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/mesh.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/flags.make
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/mesh.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/mesh.c
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/mesh.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/mesh.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/mesh.c.o -MF CMakeFiles/glu-libtess.dir/src/mesh.c.o.d -o CMakeFiles/glu-libtess.dir/src/mesh.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/mesh.c

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/mesh.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/glu-libtess.dir/src/mesh.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/mesh.c > CMakeFiles/glu-libtess.dir/src/mesh.c.i

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/mesh.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/glu-libtess.dir/src/mesh.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/mesh.c -o CMakeFiles/glu-libtess.dir/src/mesh.c.s

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/normal.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/flags.make
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/normal.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/normal.c
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/normal.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/normal.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/normal.c.o -MF CMakeFiles/glu-libtess.dir/src/normal.c.o.d -o CMakeFiles/glu-libtess.dir/src/normal.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/normal.c

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/normal.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/glu-libtess.dir/src/normal.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/normal.c > CMakeFiles/glu-libtess.dir/src/normal.c.i

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/normal.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/glu-libtess.dir/src/normal.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/normal.c -o CMakeFiles/glu-libtess.dir/src/normal.c.s

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/priorityq.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/flags.make
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/priorityq.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/priorityq.c
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/priorityq.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/priorityq.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/priorityq.c.o -MF CMakeFiles/glu-libtess.dir/src/priorityq.c.o.d -o CMakeFiles/glu-libtess.dir/src/priorityq.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/priorityq.c

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/priorityq.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/glu-libtess.dir/src/priorityq.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/priorityq.c > CMakeFiles/glu-libtess.dir/src/priorityq.c.i

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/priorityq.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/glu-libtess.dir/src/priorityq.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/priorityq.c -o CMakeFiles/glu-libtess.dir/src/priorityq.c.s

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/render.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/flags.make
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/render.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/render.c
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/render.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/render.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/render.c.o -MF CMakeFiles/glu-libtess.dir/src/render.c.o.d -o CMakeFiles/glu-libtess.dir/src/render.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/render.c

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/render.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/glu-libtess.dir/src/render.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/render.c > CMakeFiles/glu-libtess.dir/src/render.c.i

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/render.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/glu-libtess.dir/src/render.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/render.c -o CMakeFiles/glu-libtess.dir/src/render.c.s

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/sweep.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/flags.make
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/sweep.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/sweep.c
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/sweep.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/sweep.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/sweep.c.o -MF CMakeFiles/glu-libtess.dir/src/sweep.c.o.d -o CMakeFiles/glu-libtess.dir/src/sweep.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/sweep.c

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/sweep.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/glu-libtess.dir/src/sweep.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/sweep.c > CMakeFiles/glu-libtess.dir/src/sweep.c.i

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/sweep.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/glu-libtess.dir/src/sweep.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/sweep.c -o CMakeFiles/glu-libtess.dir/src/sweep.c.s

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tess.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/flags.make
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tess.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/tess.c
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tess.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tess.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tess.c.o -MF CMakeFiles/glu-libtess.dir/src/tess.c.o.d -o CMakeFiles/glu-libtess.dir/src/tess.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/tess.c

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tess.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/glu-libtess.dir/src/tess.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/tess.c > CMakeFiles/glu-libtess.dir/src/tess.c.i

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tess.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/glu-libtess.dir/src/tess.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/tess.c -o CMakeFiles/glu-libtess.dir/src/tess.c.s

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tessmono.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/flags.make
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tessmono.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/tessmono.c
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tessmono.c.o: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tessmono.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tessmono.c.o -MF CMakeFiles/glu-libtess.dir/src/tessmono.c.o.d -o CMakeFiles/glu-libtess.dir/src/tessmono.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/tessmono.c

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tessmono.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/glu-libtess.dir/src/tessmono.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/tessmono.c > CMakeFiles/glu-libtess.dir/src/tessmono.c.i

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tessmono.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/glu-libtess.dir/src/tessmono.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/tessmono.c -o CMakeFiles/glu-libtess.dir/src/tessmono.c.s

# Object files for target glu-libtess
glu__libtess_OBJECTS = \
"CMakeFiles/glu-libtess.dir/src/dict.c.o" \
"CMakeFiles/glu-libtess.dir/src/geom.c.o" \
"CMakeFiles/glu-libtess.dir/src/memalloc.c.o" \
"CMakeFiles/glu-libtess.dir/src/mesh.c.o" \
"CMakeFiles/glu-libtess.dir/src/normal.c.o" \
"CMakeFiles/glu-libtess.dir/src/priorityq.c.o" \
"CMakeFiles/glu-libtess.dir/src/render.c.o" \
"CMakeFiles/glu-libtess.dir/src/sweep.c.o" \
"CMakeFiles/glu-libtess.dir/src/tess.c.o" \
"CMakeFiles/glu-libtess.dir/src/tessmono.c.o"

# External object files for target glu-libtess
glu__libtess_EXTERNAL_OBJECTS =

bundled_deps/glu-libtess/libglu-libtess.a: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/dict.c.o
bundled_deps/glu-libtess/libglu-libtess.a: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/geom.c.o
bundled_deps/glu-libtess/libglu-libtess.a: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/memalloc.c.o
bundled_deps/glu-libtess/libglu-libtess.a: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/mesh.c.o
bundled_deps/glu-libtess/libglu-libtess.a: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/normal.c.o
bundled_deps/glu-libtess/libglu-libtess.a: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/priorityq.c.o
bundled_deps/glu-libtess/libglu-libtess.a: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/render.c.o
bundled_deps/glu-libtess/libglu-libtess.a: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/sweep.c.o
bundled_deps/glu-libtess/libglu-libtess.a: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tess.c.o
bundled_deps/glu-libtess/libglu-libtess.a: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/tessmono.c.o
bundled_deps/glu-libtess/libglu-libtess.a: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build.make
bundled_deps/glu-libtess/libglu-libtess.a: bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Linking C static library libglu-libtess.a"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && $(CMAKE_COMMAND) -P CMakeFiles/glu-libtess.dir/cmake_clean_target.cmake
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/glu-libtess.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build: bundled_deps/glu-libtess/libglu-libtess.a
.PHONY : bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/build

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess && $(CMAKE_COMMAND) -P CMakeFiles/glu-libtess.dir/cmake_clean.cmake
.PHONY : bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/clean

bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/depend

