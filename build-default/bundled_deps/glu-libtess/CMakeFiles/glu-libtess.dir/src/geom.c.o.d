bundled_deps/glu-libtess/CMakeFiles/glu-libtess.dir/src/geom.c.o: \
  /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/geom.c \
  /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/gluos.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/assert.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/sys/cdefs.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/sys/_symbol_aliasing.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/sys/_posix_availability.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/ptrcheck.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/_static_assert.h \
  /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/mesh.h \
  /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/include/glu-libtess.h \
  /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/src/geom.h
