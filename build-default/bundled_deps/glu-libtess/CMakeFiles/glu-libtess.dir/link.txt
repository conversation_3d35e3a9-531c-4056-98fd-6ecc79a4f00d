/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar qc libglu-libtess.a "CMakeFiles/glu-libtess.dir/src/dict.c.o" "CMakeFiles/glu-libtess.dir/src/geom.c.o" "CMakeFiles/glu-libtess.dir/src/memalloc.c.o" "CMakeFiles/glu-libtess.dir/src/mesh.c.o" "CMakeFiles/glu-libtess.dir/src/normal.c.o" "CMakeFiles/glu-libtess.dir/src/priorityq.c.o" "CMakeFiles/glu-libtess.dir/src/render.c.o" "CMakeFiles/glu-libtess.dir/src/sweep.c.o" "CMakeFiles/glu-libtess.dir/src/tess.c.o" "CMakeFiles/glu-libtess.dir/src/tessmono.c.o"
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib libglu-libtess.a
