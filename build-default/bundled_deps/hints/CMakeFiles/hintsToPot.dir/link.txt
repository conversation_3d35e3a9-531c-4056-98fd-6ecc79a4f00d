/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/hintsToPot.dir/HintsToPot.cpp.o -o hintsToPot  /opt/homebrew/lib/libboost_log.a /opt/homebrew/lib/libboost_filesystem.a /opt/homebrew/lib/libboost_locale.a /opt/homebrew/lib/libboost_thread.a -liconv /opt/homebrew/lib/libboost_charconv.a /opt/homebrew/lib/libboost_chrono.a /opt/homebrew/lib/libboost_atomic.a /opt/homebrew/lib/libboost_date_time.a /opt/homebrew/lib/libboost_iostreams.a /opt/homebrew/lib/libboost_regex.a -lbz2 -licudata -licui18n -licuuc -llzma -lz -lzstd /opt/homebrew/lib/libboost_random.a /opt/homebrew/lib/libboost_system.a /opt/homebrew/lib/libboost_nowide.a 
