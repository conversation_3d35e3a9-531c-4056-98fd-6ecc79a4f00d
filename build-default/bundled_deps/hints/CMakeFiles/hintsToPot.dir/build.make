# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Include any dependencies generated for this target.
include bundled_deps/hints/CMakeFiles/hintsToPot.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include bundled_deps/hints/CMakeFiles/hintsToPot.dir/compiler_depend.make

# Include the progress variables for this target.
include bundled_deps/hints/CMakeFiles/hintsToPot.dir/progress.make

# Include the compile flags for this target's objects.
include bundled_deps/hints/CMakeFiles/hintsToPot.dir/flags.make

bundled_deps/hints/CMakeFiles/hintsToPot.dir/HintsToPot.cpp.o: bundled_deps/hints/CMakeFiles/hintsToPot.dir/flags.make
bundled_deps/hints/CMakeFiles/hintsToPot.dir/HintsToPot.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/hints/HintsToPot.cpp
bundled_deps/hints/CMakeFiles/hintsToPot.dir/HintsToPot.cpp.o: bundled_deps/hints/CMakeFiles/hintsToPot.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object bundled_deps/hints/CMakeFiles/hintsToPot.dir/HintsToPot.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hints && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT bundled_deps/hints/CMakeFiles/hintsToPot.dir/HintsToPot.cpp.o -MF CMakeFiles/hintsToPot.dir/HintsToPot.cpp.o.d -o CMakeFiles/hintsToPot.dir/HintsToPot.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/hints/HintsToPot.cpp

bundled_deps/hints/CMakeFiles/hintsToPot.dir/HintsToPot.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/hintsToPot.dir/HintsToPot.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hints && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/hints/HintsToPot.cpp > CMakeFiles/hintsToPot.dir/HintsToPot.cpp.i

bundled_deps/hints/CMakeFiles/hintsToPot.dir/HintsToPot.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/hintsToPot.dir/HintsToPot.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hints && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/hints/HintsToPot.cpp -o CMakeFiles/hintsToPot.dir/HintsToPot.cpp.s

# Object files for target hintsToPot
hintsToPot_OBJECTS = \
"CMakeFiles/hintsToPot.dir/HintsToPot.cpp.o"

# External object files for target hintsToPot
hintsToPot_EXTERNAL_OBJECTS =

bundled_deps/hints/hintsToPot: bundled_deps/hints/CMakeFiles/hintsToPot.dir/HintsToPot.cpp.o
bundled_deps/hints/hintsToPot: bundled_deps/hints/CMakeFiles/hintsToPot.dir/build.make
bundled_deps/hints/hintsToPot: /opt/homebrew/lib/libboost_log.a
bundled_deps/hints/hintsToPot: /opt/homebrew/lib/libboost_filesystem.a
bundled_deps/hints/hintsToPot: /opt/homebrew/lib/libboost_locale.a
bundled_deps/hints/hintsToPot: /opt/homebrew/lib/libboost_thread.a
bundled_deps/hints/hintsToPot: /opt/homebrew/lib/libboost_charconv.a
bundled_deps/hints/hintsToPot: /opt/homebrew/lib/libboost_chrono.a
bundled_deps/hints/hintsToPot: /opt/homebrew/lib/libboost_atomic.a
bundled_deps/hints/hintsToPot: /opt/homebrew/lib/libboost_date_time.a
bundled_deps/hints/hintsToPot: /opt/homebrew/lib/libboost_iostreams.a
bundled_deps/hints/hintsToPot: /opt/homebrew/lib/libboost_regex.a
bundled_deps/hints/hintsToPot: /opt/homebrew/lib/libboost_random.a
bundled_deps/hints/hintsToPot: /opt/homebrew/lib/libboost_system.a
bundled_deps/hints/hintsToPot: /opt/homebrew/lib/libboost_nowide.a
bundled_deps/hints/hintsToPot: bundled_deps/hints/CMakeFiles/hintsToPot.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable hintsToPot"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hints && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/hintsToPot.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
bundled_deps/hints/CMakeFiles/hintsToPot.dir/build: bundled_deps/hints/hintsToPot
.PHONY : bundled_deps/hints/CMakeFiles/hintsToPot.dir/build

bundled_deps/hints/CMakeFiles/hintsToPot.dir/clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hints && $(CMAKE_COMMAND) -P CMakeFiles/hintsToPot.dir/cmake_clean.cmake
.PHONY : bundled_deps/hints/CMakeFiles/hintsToPot.dir/clean

bundled_deps/hints/CMakeFiles/hintsToPot.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/hints /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hints /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hints/CMakeFiles/hintsToPot.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : bundled_deps/hints/CMakeFiles/hintsToPot.dir/depend

