# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# compile C with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc
C_DEFINES = -DSLIC3R_DESKTOP_INTEGRATION -DSLIC3R_GUI -DUNICODE -DWXINTL_NO_GETTEXT_MACRO -D_UNICODE -DwxNO_UNSAFE_WXSTRING_CONV -DwxUSE_UNICODE

C_INCLUDES = -I/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/platform -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/semver

C_FLAGSarm64 =  -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fsigned-char -Werror=return-type -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-misleading-indentation

C_FLAGS =  -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fsigned-char -Werror=return-type -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-misleading-indentation

