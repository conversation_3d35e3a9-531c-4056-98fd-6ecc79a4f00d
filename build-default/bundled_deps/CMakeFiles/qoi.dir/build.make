# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Include any dependencies generated for this target.
include bundled_deps/CMakeFiles/qoi.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include bundled_deps/CMakeFiles/qoi.dir/compiler_depend.make

# Include the progress variables for this target.
include bundled_deps/CMakeFiles/qoi.dir/progress.make

# Include the compile flags for this target's objects.
include bundled_deps/CMakeFiles/qoi.dir/flags.make

bundled_deps/CMakeFiles/qoi.dir/qoi/qoilib.c.o: bundled_deps/CMakeFiles/qoi.dir/flags.make
bundled_deps/CMakeFiles/qoi.dir/qoi/qoilib.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/qoi/qoilib.c
bundled_deps/CMakeFiles/qoi.dir/qoi/qoilib.c.o: bundled_deps/CMakeFiles/qoi.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object bundled_deps/CMakeFiles/qoi.dir/qoi/qoilib.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/CMakeFiles/qoi.dir/qoi/qoilib.c.o -MF CMakeFiles/qoi.dir/qoi/qoilib.c.o.d -o CMakeFiles/qoi.dir/qoi/qoilib.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/qoi/qoilib.c

bundled_deps/CMakeFiles/qoi.dir/qoi/qoilib.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qoi.dir/qoi/qoilib.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/qoi/qoilib.c > CMakeFiles/qoi.dir/qoi/qoilib.c.i

bundled_deps/CMakeFiles/qoi.dir/qoi/qoilib.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qoi.dir/qoi/qoilib.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/qoi/qoilib.c -o CMakeFiles/qoi.dir/qoi/qoilib.c.s

# Object files for target qoi
qoi_OBJECTS = \
"CMakeFiles/qoi.dir/qoi/qoilib.c.o"

# External object files for target qoi
qoi_EXTERNAL_OBJECTS =

bundled_deps/libqoi.a: bundled_deps/CMakeFiles/qoi.dir/qoi/qoilib.c.o
bundled_deps/libqoi.a: bundled_deps/CMakeFiles/qoi.dir/build.make
bundled_deps/libqoi.a: bundled_deps/CMakeFiles/qoi.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C static library libqoi.a"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps && $(CMAKE_COMMAND) -P CMakeFiles/qoi.dir/cmake_clean_target.cmake
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/qoi.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
bundled_deps/CMakeFiles/qoi.dir/build: bundled_deps/libqoi.a
.PHONY : bundled_deps/CMakeFiles/qoi.dir/build

bundled_deps/CMakeFiles/qoi.dir/clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps && $(CMAKE_COMMAND) -P CMakeFiles/qoi.dir/cmake_clean.cmake
.PHONY : bundled_deps/CMakeFiles/qoi.dir/clean

bundled_deps/CMakeFiles/qoi.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/CMakeFiles/qoi.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : bundled_deps/CMakeFiles/qoi.dir/depend

