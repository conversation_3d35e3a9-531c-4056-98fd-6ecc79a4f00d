
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui.cpp" "bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui.cpp.o" "gcc" "bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_demo.cpp" "bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.o" "gcc" "bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_draw.cpp" "bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.o" "gcc" "bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_stdlib.cpp" "bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.o" "gcc" "bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_tables.cpp" "bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.o" "gcc" "bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_widgets.cpp" "bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.o" "gcc" "bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
