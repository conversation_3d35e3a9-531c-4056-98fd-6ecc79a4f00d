# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Include any dependencies generated for this target.
include bundled_deps/imgui/CMakeFiles/imgui.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include bundled_deps/imgui/CMakeFiles/imgui.dir/compiler_depend.make

# Include the progress variables for this target.
include bundled_deps/imgui/CMakeFiles/imgui.dir/progress.make

# Include the compile flags for this target's objects.
include bundled_deps/imgui/CMakeFiles/imgui.dir/flags.make

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui.cpp.o: bundled_deps/imgui/CMakeFiles/imgui.dir/flags.make
bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui.cpp
bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui.cpp.o: bundled_deps/imgui/CMakeFiles/imgui.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui.cpp.o -MF CMakeFiles/imgui.dir/imgui/imgui.cpp.o.d -o CMakeFiles/imgui.dir/imgui/imgui.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui.cpp

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/imgui.dir/imgui/imgui.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui.cpp > CMakeFiles/imgui.dir/imgui/imgui.cpp.i

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/imgui.dir/imgui/imgui.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui.cpp -o CMakeFiles/imgui.dir/imgui/imgui.cpp.s

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.o: bundled_deps/imgui/CMakeFiles/imgui.dir/flags.make
bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_demo.cpp
bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.o: bundled_deps/imgui/CMakeFiles/imgui.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.o -MF CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.o.d -o CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_demo.cpp

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_demo.cpp > CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.i

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_demo.cpp -o CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.s

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.o: bundled_deps/imgui/CMakeFiles/imgui.dir/flags.make
bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_draw.cpp
bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.o: bundled_deps/imgui/CMakeFiles/imgui.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.o -MF CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.o.d -o CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_draw.cpp

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_draw.cpp > CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.i

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_draw.cpp -o CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.s

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.o: bundled_deps/imgui/CMakeFiles/imgui.dir/flags.make
bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_stdlib.cpp
bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.o: bundled_deps/imgui/CMakeFiles/imgui.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.o -MF CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.o.d -o CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_stdlib.cpp

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_stdlib.cpp > CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.i

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_stdlib.cpp -o CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.s

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.o: bundled_deps/imgui/CMakeFiles/imgui.dir/flags.make
bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_tables.cpp
bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.o: bundled_deps/imgui/CMakeFiles/imgui.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.o -MF CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.o.d -o CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_tables.cpp

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_tables.cpp > CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.i

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_tables.cpp -o CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.s

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.o: bundled_deps/imgui/CMakeFiles/imgui.dir/flags.make
bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_widgets.cpp
bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.o: bundled_deps/imgui/CMakeFiles/imgui.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.o -MF CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.o.d -o CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_widgets.cpp

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_widgets.cpp > CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.i

bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui/imgui/imgui_widgets.cpp -o CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.s

# Object files for target imgui
imgui_OBJECTS = \
"CMakeFiles/imgui.dir/imgui/imgui.cpp.o" \
"CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.o" \
"CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.o" \
"CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.o" \
"CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.o" \
"CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.o"

# External object files for target imgui
imgui_EXTERNAL_OBJECTS =

bundled_deps/imgui/libimgui.a: bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui.cpp.o
bundled_deps/imgui/libimgui.a: bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.o
bundled_deps/imgui/libimgui.a: bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.o
bundled_deps/imgui/libimgui.a: bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.o
bundled_deps/imgui/libimgui.a: bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.o
bundled_deps/imgui/libimgui.a: bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.o
bundled_deps/imgui/libimgui.a: bundled_deps/imgui/CMakeFiles/imgui.dir/build.make
bundled_deps/imgui/libimgui.a: bundled_deps/imgui/CMakeFiles/imgui.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX static library libimgui.a"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && $(CMAKE_COMMAND) -P CMakeFiles/imgui.dir/cmake_clean_target.cmake
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/imgui.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
bundled_deps/imgui/CMakeFiles/imgui.dir/build: bundled_deps/imgui/libimgui.a
.PHONY : bundled_deps/imgui/CMakeFiles/imgui.dir/build

bundled_deps/imgui/CMakeFiles/imgui.dir/clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui && $(CMAKE_COMMAND) -P CMakeFiles/imgui.dir/cmake_clean.cmake
.PHONY : bundled_deps/imgui/CMakeFiles/imgui.dir/clean

bundled_deps/imgui/CMakeFiles/imgui.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/imgui /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui/CMakeFiles/imgui.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : bundled_deps/imgui/CMakeFiles/imgui.dir/depend

