# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/Applications/CMake.app/Contents/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/Applications/CMake.app/Contents/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/Applications/CMake.app/Contents/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/imgui//CMakeFiles/progress.marks
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/imgui/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/imgui/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/imgui/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/imgui/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
bundled_deps/imgui/CMakeFiles/imgui.dir/rule:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/imgui/CMakeFiles/imgui.dir/rule
.PHONY : bundled_deps/imgui/CMakeFiles/imgui.dir/rule

# Convenience name for target.
imgui: bundled_deps/imgui/CMakeFiles/imgui.dir/rule
.PHONY : imgui

# fast build rule for target.
imgui/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/build
.PHONY : imgui/fast

imgui/imgui.o: imgui/imgui.cpp.o
.PHONY : imgui/imgui.o

# target to build an object file
imgui/imgui.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui.cpp.o
.PHONY : imgui/imgui.cpp.o

imgui/imgui.i: imgui/imgui.cpp.i
.PHONY : imgui/imgui.i

# target to preprocess a source file
imgui/imgui.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui.cpp.i
.PHONY : imgui/imgui.cpp.i

imgui/imgui.s: imgui/imgui.cpp.s
.PHONY : imgui/imgui.s

# target to generate assembly for a file
imgui/imgui.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui.cpp.s
.PHONY : imgui/imgui.cpp.s

imgui/imgui_demo.o: imgui/imgui_demo.cpp.o
.PHONY : imgui/imgui_demo.o

# target to build an object file
imgui/imgui_demo.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.o
.PHONY : imgui/imgui_demo.cpp.o

imgui/imgui_demo.i: imgui/imgui_demo.cpp.i
.PHONY : imgui/imgui_demo.i

# target to preprocess a source file
imgui/imgui_demo.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.i
.PHONY : imgui/imgui_demo.cpp.i

imgui/imgui_demo.s: imgui/imgui_demo.cpp.s
.PHONY : imgui/imgui_demo.s

# target to generate assembly for a file
imgui/imgui_demo.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_demo.cpp.s
.PHONY : imgui/imgui_demo.cpp.s

imgui/imgui_draw.o: imgui/imgui_draw.cpp.o
.PHONY : imgui/imgui_draw.o

# target to build an object file
imgui/imgui_draw.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.o
.PHONY : imgui/imgui_draw.cpp.o

imgui/imgui_draw.i: imgui/imgui_draw.cpp.i
.PHONY : imgui/imgui_draw.i

# target to preprocess a source file
imgui/imgui_draw.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.i
.PHONY : imgui/imgui_draw.cpp.i

imgui/imgui_draw.s: imgui/imgui_draw.cpp.s
.PHONY : imgui/imgui_draw.s

# target to generate assembly for a file
imgui/imgui_draw.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_draw.cpp.s
.PHONY : imgui/imgui_draw.cpp.s

imgui/imgui_stdlib.o: imgui/imgui_stdlib.cpp.o
.PHONY : imgui/imgui_stdlib.o

# target to build an object file
imgui/imgui_stdlib.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.o
.PHONY : imgui/imgui_stdlib.cpp.o

imgui/imgui_stdlib.i: imgui/imgui_stdlib.cpp.i
.PHONY : imgui/imgui_stdlib.i

# target to preprocess a source file
imgui/imgui_stdlib.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.i
.PHONY : imgui/imgui_stdlib.cpp.i

imgui/imgui_stdlib.s: imgui/imgui_stdlib.cpp.s
.PHONY : imgui/imgui_stdlib.s

# target to generate assembly for a file
imgui/imgui_stdlib.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_stdlib.cpp.s
.PHONY : imgui/imgui_stdlib.cpp.s

imgui/imgui_tables.o: imgui/imgui_tables.cpp.o
.PHONY : imgui/imgui_tables.o

# target to build an object file
imgui/imgui_tables.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.o
.PHONY : imgui/imgui_tables.cpp.o

imgui/imgui_tables.i: imgui/imgui_tables.cpp.i
.PHONY : imgui/imgui_tables.i

# target to preprocess a source file
imgui/imgui_tables.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.i
.PHONY : imgui/imgui_tables.cpp.i

imgui/imgui_tables.s: imgui/imgui_tables.cpp.s
.PHONY : imgui/imgui_tables.s

# target to generate assembly for a file
imgui/imgui_tables.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_tables.cpp.s
.PHONY : imgui/imgui_tables.cpp.s

imgui/imgui_widgets.o: imgui/imgui_widgets.cpp.o
.PHONY : imgui/imgui_widgets.o

# target to build an object file
imgui/imgui_widgets.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.o
.PHONY : imgui/imgui_widgets.cpp.o

imgui/imgui_widgets.i: imgui/imgui_widgets.cpp.i
.PHONY : imgui/imgui_widgets.i

# target to preprocess a source file
imgui/imgui_widgets.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.i
.PHONY : imgui/imgui_widgets.cpp.i

imgui/imgui_widgets.s: imgui/imgui_widgets.cpp.s
.PHONY : imgui/imgui_widgets.s

# target to generate assembly for a file
imgui/imgui_widgets.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/imgui/CMakeFiles/imgui.dir/build.make bundled_deps/imgui/CMakeFiles/imgui.dir/imgui/imgui_widgets.cpp.s
.PHONY : imgui/imgui_widgets.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... imgui"
	@echo "... imgui/imgui.o"
	@echo "... imgui/imgui.i"
	@echo "... imgui/imgui.s"
	@echo "... imgui/imgui_demo.o"
	@echo "... imgui/imgui_demo.i"
	@echo "... imgui/imgui_demo.s"
	@echo "... imgui/imgui_draw.o"
	@echo "... imgui/imgui_draw.i"
	@echo "... imgui/imgui_draw.s"
	@echo "... imgui/imgui_stdlib.o"
	@echo "... imgui/imgui_stdlib.i"
	@echo "... imgui/imgui_stdlib.s"
	@echo "... imgui/imgui_tables.o"
	@echo "... imgui/imgui_tables.i"
	@echo "... imgui/imgui_tables.s"
	@echo "... imgui/imgui_widgets.o"
	@echo "... imgui/imgui_widgets.i"
	@echo "... imgui/imgui_widgets.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

