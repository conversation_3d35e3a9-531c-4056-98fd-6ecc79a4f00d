
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/connect.cpp" "bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/connect.cpp.o" "gcc" "bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/connect.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/normals.cpp" "bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/normals.cpp.o" "gcc" "bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/normals.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/shared.cpp" "bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/shared.cpp.o" "gcc" "bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/shared.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/stl_io.cpp" "bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stl_io.cpp.o" "gcc" "bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stl_io.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/stlinit.cpp" "bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stlinit.cpp.o" "gcc" "bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stlinit.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/util.cpp" "bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/util.cpp.o" "gcc" "bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/util.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
