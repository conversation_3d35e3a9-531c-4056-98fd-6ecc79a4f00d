# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Include any dependencies generated for this target.
include bundled_deps/admesh/CMakeFiles/admesh.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include bundled_deps/admesh/CMakeFiles/admesh.dir/compiler_depend.make

# Include the progress variables for this target.
include bundled_deps/admesh/CMakeFiles/admesh.dir/progress.make

# Include the compile flags for this target's objects.
include bundled_deps/admesh/CMakeFiles/admesh.dir/flags.make

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/connect.cpp.o: bundled_deps/admesh/CMakeFiles/admesh.dir/flags.make
bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/connect.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/connect.cpp
bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/connect.cpp.o: bundled_deps/admesh/CMakeFiles/admesh.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/connect.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/connect.cpp.o -MF CMakeFiles/admesh.dir/admesh/connect.cpp.o.d -o CMakeFiles/admesh.dir/admesh/connect.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/connect.cpp

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/connect.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/admesh.dir/admesh/connect.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/connect.cpp > CMakeFiles/admesh.dir/admesh/connect.cpp.i

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/connect.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/admesh.dir/admesh/connect.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/connect.cpp -o CMakeFiles/admesh.dir/admesh/connect.cpp.s

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/normals.cpp.o: bundled_deps/admesh/CMakeFiles/admesh.dir/flags.make
bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/normals.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/normals.cpp
bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/normals.cpp.o: bundled_deps/admesh/CMakeFiles/admesh.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/normals.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/normals.cpp.o -MF CMakeFiles/admesh.dir/admesh/normals.cpp.o.d -o CMakeFiles/admesh.dir/admesh/normals.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/normals.cpp

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/normals.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/admesh.dir/admesh/normals.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/normals.cpp > CMakeFiles/admesh.dir/admesh/normals.cpp.i

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/normals.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/admesh.dir/admesh/normals.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/normals.cpp -o CMakeFiles/admesh.dir/admesh/normals.cpp.s

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/shared.cpp.o: bundled_deps/admesh/CMakeFiles/admesh.dir/flags.make
bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/shared.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/shared.cpp
bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/shared.cpp.o: bundled_deps/admesh/CMakeFiles/admesh.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/shared.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/shared.cpp.o -MF CMakeFiles/admesh.dir/admesh/shared.cpp.o.d -o CMakeFiles/admesh.dir/admesh/shared.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/shared.cpp

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/shared.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/admesh.dir/admesh/shared.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/shared.cpp > CMakeFiles/admesh.dir/admesh/shared.cpp.i

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/shared.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/admesh.dir/admesh/shared.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/shared.cpp -o CMakeFiles/admesh.dir/admesh/shared.cpp.s

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stl_io.cpp.o: bundled_deps/admesh/CMakeFiles/admesh.dir/flags.make
bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stl_io.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/stl_io.cpp
bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stl_io.cpp.o: bundled_deps/admesh/CMakeFiles/admesh.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stl_io.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stl_io.cpp.o -MF CMakeFiles/admesh.dir/admesh/stl_io.cpp.o.d -o CMakeFiles/admesh.dir/admesh/stl_io.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/stl_io.cpp

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stl_io.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/admesh.dir/admesh/stl_io.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/stl_io.cpp > CMakeFiles/admesh.dir/admesh/stl_io.cpp.i

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stl_io.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/admesh.dir/admesh/stl_io.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/stl_io.cpp -o CMakeFiles/admesh.dir/admesh/stl_io.cpp.s

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stlinit.cpp.o: bundled_deps/admesh/CMakeFiles/admesh.dir/flags.make
bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stlinit.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/stlinit.cpp
bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stlinit.cpp.o: bundled_deps/admesh/CMakeFiles/admesh.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stlinit.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stlinit.cpp.o -MF CMakeFiles/admesh.dir/admesh/stlinit.cpp.o.d -o CMakeFiles/admesh.dir/admesh/stlinit.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/stlinit.cpp

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stlinit.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/admesh.dir/admesh/stlinit.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/stlinit.cpp > CMakeFiles/admesh.dir/admesh/stlinit.cpp.i

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stlinit.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/admesh.dir/admesh/stlinit.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/stlinit.cpp -o CMakeFiles/admesh.dir/admesh/stlinit.cpp.s

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/util.cpp.o: bundled_deps/admesh/CMakeFiles/admesh.dir/flags.make
bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/util.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/util.cpp
bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/util.cpp.o: bundled_deps/admesh/CMakeFiles/admesh.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/util.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/util.cpp.o -MF CMakeFiles/admesh.dir/admesh/util.cpp.o.d -o CMakeFiles/admesh.dir/admesh/util.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/util.cpp

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/util.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/admesh.dir/admesh/util.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/util.cpp > CMakeFiles/admesh.dir/admesh/util.cpp.i

bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/util.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/admesh.dir/admesh/util.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/admesh/util.cpp -o CMakeFiles/admesh.dir/admesh/util.cpp.s

# Object files for target admesh
admesh_OBJECTS = \
"CMakeFiles/admesh.dir/admesh/connect.cpp.o" \
"CMakeFiles/admesh.dir/admesh/normals.cpp.o" \
"CMakeFiles/admesh.dir/admesh/shared.cpp.o" \
"CMakeFiles/admesh.dir/admesh/stl_io.cpp.o" \
"CMakeFiles/admesh.dir/admesh/stlinit.cpp.o" \
"CMakeFiles/admesh.dir/admesh/util.cpp.o"

# External object files for target admesh
admesh_EXTERNAL_OBJECTS =

bundled_deps/admesh/libadmesh.a: bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/connect.cpp.o
bundled_deps/admesh/libadmesh.a: bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/normals.cpp.o
bundled_deps/admesh/libadmesh.a: bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/shared.cpp.o
bundled_deps/admesh/libadmesh.a: bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stl_io.cpp.o
bundled_deps/admesh/libadmesh.a: bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stlinit.cpp.o
bundled_deps/admesh/libadmesh.a: bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/util.cpp.o
bundled_deps/admesh/libadmesh.a: bundled_deps/admesh/CMakeFiles/admesh.dir/build.make
bundled_deps/admesh/libadmesh.a: bundled_deps/admesh/CMakeFiles/admesh.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX static library libadmesh.a"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && $(CMAKE_COMMAND) -P CMakeFiles/admesh.dir/cmake_clean_target.cmake
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/admesh.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
bundled_deps/admesh/CMakeFiles/admesh.dir/build: bundled_deps/admesh/libadmesh.a
.PHONY : bundled_deps/admesh/CMakeFiles/admesh.dir/build

bundled_deps/admesh/CMakeFiles/admesh.dir/clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh && $(CMAKE_COMMAND) -P CMakeFiles/admesh.dir/cmake_clean.cmake
.PHONY : bundled_deps/admesh/CMakeFiles/admesh.dir/clean

bundled_deps/admesh/CMakeFiles/admesh.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh/CMakeFiles/admesh.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : bundled_deps/admesh/CMakeFiles/admesh.dir/depend

