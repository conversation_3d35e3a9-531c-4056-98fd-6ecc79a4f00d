# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/Applications/CMake.app/Contents/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/Applications/CMake.app/Contents/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/Applications/CMake.app/Contents/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/admesh//CMakeFiles/progress.marks
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/admesh/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/admesh/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/admesh/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/admesh/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
bundled_deps/admesh/CMakeFiles/admesh.dir/rule:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/admesh/CMakeFiles/admesh.dir/rule
.PHONY : bundled_deps/admesh/CMakeFiles/admesh.dir/rule

# Convenience name for target.
admesh: bundled_deps/admesh/CMakeFiles/admesh.dir/rule
.PHONY : admesh

# fast build rule for target.
admesh/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/build
.PHONY : admesh/fast

admesh/connect.o: admesh/connect.cpp.o
.PHONY : admesh/connect.o

# target to build an object file
admesh/connect.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/connect.cpp.o
.PHONY : admesh/connect.cpp.o

admesh/connect.i: admesh/connect.cpp.i
.PHONY : admesh/connect.i

# target to preprocess a source file
admesh/connect.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/connect.cpp.i
.PHONY : admesh/connect.cpp.i

admesh/connect.s: admesh/connect.cpp.s
.PHONY : admesh/connect.s

# target to generate assembly for a file
admesh/connect.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/connect.cpp.s
.PHONY : admesh/connect.cpp.s

admesh/normals.o: admesh/normals.cpp.o
.PHONY : admesh/normals.o

# target to build an object file
admesh/normals.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/normals.cpp.o
.PHONY : admesh/normals.cpp.o

admesh/normals.i: admesh/normals.cpp.i
.PHONY : admesh/normals.i

# target to preprocess a source file
admesh/normals.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/normals.cpp.i
.PHONY : admesh/normals.cpp.i

admesh/normals.s: admesh/normals.cpp.s
.PHONY : admesh/normals.s

# target to generate assembly for a file
admesh/normals.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/normals.cpp.s
.PHONY : admesh/normals.cpp.s

admesh/shared.o: admesh/shared.cpp.o
.PHONY : admesh/shared.o

# target to build an object file
admesh/shared.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/shared.cpp.o
.PHONY : admesh/shared.cpp.o

admesh/shared.i: admesh/shared.cpp.i
.PHONY : admesh/shared.i

# target to preprocess a source file
admesh/shared.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/shared.cpp.i
.PHONY : admesh/shared.cpp.i

admesh/shared.s: admesh/shared.cpp.s
.PHONY : admesh/shared.s

# target to generate assembly for a file
admesh/shared.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/shared.cpp.s
.PHONY : admesh/shared.cpp.s

admesh/stl_io.o: admesh/stl_io.cpp.o
.PHONY : admesh/stl_io.o

# target to build an object file
admesh/stl_io.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stl_io.cpp.o
.PHONY : admesh/stl_io.cpp.o

admesh/stl_io.i: admesh/stl_io.cpp.i
.PHONY : admesh/stl_io.i

# target to preprocess a source file
admesh/stl_io.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stl_io.cpp.i
.PHONY : admesh/stl_io.cpp.i

admesh/stl_io.s: admesh/stl_io.cpp.s
.PHONY : admesh/stl_io.s

# target to generate assembly for a file
admesh/stl_io.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stl_io.cpp.s
.PHONY : admesh/stl_io.cpp.s

admesh/stlinit.o: admesh/stlinit.cpp.o
.PHONY : admesh/stlinit.o

# target to build an object file
admesh/stlinit.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stlinit.cpp.o
.PHONY : admesh/stlinit.cpp.o

admesh/stlinit.i: admesh/stlinit.cpp.i
.PHONY : admesh/stlinit.i

# target to preprocess a source file
admesh/stlinit.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stlinit.cpp.i
.PHONY : admesh/stlinit.cpp.i

admesh/stlinit.s: admesh/stlinit.cpp.s
.PHONY : admesh/stlinit.s

# target to generate assembly for a file
admesh/stlinit.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/stlinit.cpp.s
.PHONY : admesh/stlinit.cpp.s

admesh/util.o: admesh/util.cpp.o
.PHONY : admesh/util.o

# target to build an object file
admesh/util.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/util.cpp.o
.PHONY : admesh/util.cpp.o

admesh/util.i: admesh/util.cpp.i
.PHONY : admesh/util.i

# target to preprocess a source file
admesh/util.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/util.cpp.i
.PHONY : admesh/util.cpp.i

admesh/util.s: admesh/util.cpp.s
.PHONY : admesh/util.s

# target to generate assembly for a file
admesh/util.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/admesh/CMakeFiles/admesh.dir/build.make bundled_deps/admesh/CMakeFiles/admesh.dir/admesh/util.cpp.s
.PHONY : admesh/util.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... admesh"
	@echo "... admesh/connect.o"
	@echo "... admesh/connect.i"
	@echo "... admesh/connect.s"
	@echo "... admesh/normals.o"
	@echo "... admesh/normals.i"
	@echo "... admesh/normals.s"
	@echo "... admesh/shared.o"
	@echo "... admesh/shared.i"
	@echo "... admesh/shared.s"
	@echo "... admesh/stl_io.o"
	@echo "... admesh/stl_io.i"
	@echo "... admesh/stl_io.s"
	@echo "... admesh/stlinit.o"
	@echo "... admesh/stlinit.i"
	@echo "... admesh/stlinit.s"
	@echo "... admesh/util.o"
	@echo "... admesh/util.i"
	@echo "... admesh/util.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

