# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Include any dependencies generated for this target.
include bundled_deps/miniz/CMakeFiles/miniz_static.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include bundled_deps/miniz/CMakeFiles/miniz_static.dir/compiler_depend.make

# Include the progress variables for this target.
include bundled_deps/miniz/CMakeFiles/miniz_static.dir/progress.make

# Include the compile flags for this target's objects.
include bundled_deps/miniz/CMakeFiles/miniz_static.dir/flags.make

bundled_deps/miniz/CMakeFiles/miniz_static.dir/miniz.c.o: bundled_deps/miniz/CMakeFiles/miniz_static.dir/flags.make
bundled_deps/miniz/CMakeFiles/miniz_static.dir/miniz.c.o: /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/miniz/miniz.c
bundled_deps/miniz/CMakeFiles/miniz_static.dir/miniz.c.o: bundled_deps/miniz/CMakeFiles/miniz_static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object bundled_deps/miniz/CMakeFiles/miniz_static.dir/miniz.c.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/miniz && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT bundled_deps/miniz/CMakeFiles/miniz_static.dir/miniz.c.o -MF CMakeFiles/miniz_static.dir/miniz.c.o.d -o CMakeFiles/miniz_static.dir/miniz.c.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/miniz/miniz.c

bundled_deps/miniz/CMakeFiles/miniz_static.dir/miniz.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/miniz_static.dir/miniz.c.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/miniz && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/miniz/miniz.c > CMakeFiles/miniz_static.dir/miniz.c.i

bundled_deps/miniz/CMakeFiles/miniz_static.dir/miniz.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/miniz_static.dir/miniz.c.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/miniz && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/miniz/miniz.c -o CMakeFiles/miniz_static.dir/miniz.c.s

# Object files for target miniz_static
miniz_static_OBJECTS = \
"CMakeFiles/miniz_static.dir/miniz.c.o"

# External object files for target miniz_static
miniz_static_EXTERNAL_OBJECTS =

bundled_deps/miniz/libminiz_static.a: bundled_deps/miniz/CMakeFiles/miniz_static.dir/miniz.c.o
bundled_deps/miniz/libminiz_static.a: bundled_deps/miniz/CMakeFiles/miniz_static.dir/build.make
bundled_deps/miniz/libminiz_static.a: bundled_deps/miniz/CMakeFiles/miniz_static.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C static library libminiz_static.a"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/miniz && $(CMAKE_COMMAND) -P CMakeFiles/miniz_static.dir/cmake_clean_target.cmake
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/miniz && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/miniz_static.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
bundled_deps/miniz/CMakeFiles/miniz_static.dir/build: bundled_deps/miniz/libminiz_static.a
.PHONY : bundled_deps/miniz/CMakeFiles/miniz_static.dir/build

bundled_deps/miniz/CMakeFiles/miniz_static.dir/clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/miniz && $(CMAKE_COMMAND) -P CMakeFiles/miniz_static.dir/cmake_clean.cmake
.PHONY : bundled_deps/miniz/CMakeFiles/miniz_static.dir/clean

bundled_deps/miniz/CMakeFiles/miniz_static.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/miniz /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/miniz /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/miniz/CMakeFiles/miniz_static.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : bundled_deps/miniz/CMakeFiles/miniz_static.dir/depend

