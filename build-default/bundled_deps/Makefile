# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/Applications/CMake.app/Contents/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/Applications/CMake.app/Contents/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/Applications/CMake.app/Contents/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps//CMakeFiles/progress.marks
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
bundled_deps/CMakeFiles/semver.dir/rule:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/CMakeFiles/semver.dir/rule
.PHONY : bundled_deps/CMakeFiles/semver.dir/rule

# Convenience name for target.
semver: bundled_deps/CMakeFiles/semver.dir/rule
.PHONY : semver

# fast build rule for target.
semver/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/CMakeFiles/semver.dir/build.make bundled_deps/CMakeFiles/semver.dir/build
.PHONY : semver/fast

# Convenience name for target.
bundled_deps/CMakeFiles/qoi.dir/rule:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/CMakeFiles/qoi.dir/rule
.PHONY : bundled_deps/CMakeFiles/qoi.dir/rule

# Convenience name for target.
qoi: bundled_deps/CMakeFiles/qoi.dir/rule
.PHONY : qoi

# fast build rule for target.
qoi/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/CMakeFiles/qoi.dir/build.make bundled_deps/CMakeFiles/qoi.dir/build
.PHONY : qoi/fast

# Convenience name for target.
bundled_deps/CMakeFiles/localesutils.dir/rule:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bundled_deps/CMakeFiles/localesutils.dir/rule
.PHONY : bundled_deps/CMakeFiles/localesutils.dir/rule

# Convenience name for target.
localesutils: bundled_deps/CMakeFiles/localesutils.dir/rule
.PHONY : localesutils

# fast build rule for target.
localesutils/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/CMakeFiles/localesutils.dir/build.make bundled_deps/CMakeFiles/localesutils.dir/build
.PHONY : localesutils/fast

localesutils/LocalesUtils.o: localesutils/LocalesUtils.cpp.o
.PHONY : localesutils/LocalesUtils.o

# target to build an object file
localesutils/LocalesUtils.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/CMakeFiles/localesutils.dir/build.make bundled_deps/CMakeFiles/localesutils.dir/localesutils/LocalesUtils.cpp.o
.PHONY : localesutils/LocalesUtils.cpp.o

localesutils/LocalesUtils.i: localesutils/LocalesUtils.cpp.i
.PHONY : localesutils/LocalesUtils.i

# target to preprocess a source file
localesutils/LocalesUtils.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/CMakeFiles/localesutils.dir/build.make bundled_deps/CMakeFiles/localesutils.dir/localesutils/LocalesUtils.cpp.i
.PHONY : localesutils/LocalesUtils.cpp.i

localesutils/LocalesUtils.s: localesutils/LocalesUtils.cpp.s
.PHONY : localesutils/LocalesUtils.s

# target to generate assembly for a file
localesutils/LocalesUtils.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/CMakeFiles/localesutils.dir/build.make bundled_deps/CMakeFiles/localesutils.dir/localesutils/LocalesUtils.cpp.s
.PHONY : localesutils/LocalesUtils.cpp.s

qoi/qoilib.o: qoi/qoilib.c.o
.PHONY : qoi/qoilib.o

# target to build an object file
qoi/qoilib.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/CMakeFiles/qoi.dir/build.make bundled_deps/CMakeFiles/qoi.dir/qoi/qoilib.c.o
.PHONY : qoi/qoilib.c.o

qoi/qoilib.i: qoi/qoilib.c.i
.PHONY : qoi/qoilib.i

# target to preprocess a source file
qoi/qoilib.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/CMakeFiles/qoi.dir/build.make bundled_deps/CMakeFiles/qoi.dir/qoi/qoilib.c.i
.PHONY : qoi/qoilib.c.i

qoi/qoilib.s: qoi/qoilib.c.s
.PHONY : qoi/qoilib.s

# target to generate assembly for a file
qoi/qoilib.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/CMakeFiles/qoi.dir/build.make bundled_deps/CMakeFiles/qoi.dir/qoi/qoilib.c.s
.PHONY : qoi/qoilib.c.s

semver/semver.o: semver/semver.c.o
.PHONY : semver/semver.o

# target to build an object file
semver/semver.c.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/CMakeFiles/semver.dir/build.make bundled_deps/CMakeFiles/semver.dir/semver/semver.c.o
.PHONY : semver/semver.c.o

semver/semver.i: semver/semver.c.i
.PHONY : semver/semver.i

# target to preprocess a source file
semver/semver.c.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/CMakeFiles/semver.dir/build.make bundled_deps/CMakeFiles/semver.dir/semver/semver.c.i
.PHONY : semver/semver.c.i

semver/semver.s: semver/semver.c.s
.PHONY : semver/semver.s

# target to generate assembly for a file
semver/semver.c.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f bundled_deps/CMakeFiles/semver.dir/build.make bundled_deps/CMakeFiles/semver.dir/semver/semver.c.s
.PHONY : semver/semver.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... localesutils"
	@echo "... qoi"
	@echo "... semver"
	@echo "... localesutils/LocalesUtils.o"
	@echo "... localesutils/LocalesUtils.i"
	@echo "... localesutils/LocalesUtils.s"
	@echo "... qoi/qoilib.o"
	@echo "... qoi/qoilib.i"
	@echo "... qoi/qoilib.s"
	@echo "... semver/semver.o"
	@echo "... semver/semver.i"
	@echo "... semver/semver.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

