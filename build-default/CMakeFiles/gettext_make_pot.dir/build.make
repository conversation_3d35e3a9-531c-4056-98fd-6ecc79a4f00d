# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Utility rule file for gettext_make_pot.

# Include any custom commands dependencies for this target.
include CMakeFiles/gettext_make_pot.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/gettext_make_pot.dir/progress.make

CMakeFiles/gettext_make_pot:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generate pot file from strings in the source tree"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer && xgettext --keyword=L --keyword=_L --keyword=_u8L --keyword=L_CONTEXT:1,2c --keyword=_L_PLURAL:1,2 --add-comments=TRN --from-code=UTF-8 --debug --boost -f /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/list.txt -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	cd /Users/<USER>/Documents/myproject/PrusaSlicer && /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/bundled_deps/hints/hintsToPot /Users/<USER>/Documents/myproject/PrusaSlicer/resources /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization

gettext_make_pot: CMakeFiles/gettext_make_pot
gettext_make_pot: CMakeFiles/gettext_make_pot.dir/build.make
.PHONY : gettext_make_pot

# Rule to build all files generated by this target.
CMakeFiles/gettext_make_pot.dir/build: gettext_make_pot
.PHONY : CMakeFiles/gettext_make_pot.dir/build

CMakeFiles/gettext_make_pot.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/gettext_make_pot.dir/cmake_clean.cmake
.PHONY : CMakeFiles/gettext_make_pot.dir/clean

CMakeFiles/gettext_make_pot.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles/gettext_make_pot.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/gettext_make_pot.dir/depend

