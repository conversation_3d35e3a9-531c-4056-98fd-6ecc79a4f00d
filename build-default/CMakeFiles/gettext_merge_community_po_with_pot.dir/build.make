# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Utility rule file for gettext_merge_community_po_with_pot.

# Include any custom commands dependencies for this target.
include CMakeFiles/gettext_merge_community_po_with_pot.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/gettext_merge_community_po_with_pot.dir/progress.make

gettext_merge_community_po_with_pot: CMakeFiles/gettext_merge_community_po_with_pot.dir/build.make
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/be/PrusaSlicer_be.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/be/PrusaSlicer_be.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/be/PrusaSlicer_be.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/be/PrusaSlicer_be.po
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ca/PrusaSlicer_ca.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ca/PrusaSlicer_ca.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ca/PrusaSlicer_ca.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ca/PrusaSlicer_ca.po
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/en/PrusaSlicer_en.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/en/PrusaSlicer_en.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/en/PrusaSlicer_en.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/en/PrusaSlicer_en.po
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fi/PrusaSlicer_fi.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fi/PrusaSlicer_fi.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fi/PrusaSlicer_fi.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fi/PrusaSlicer_fi.po
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/hu/PrusaSlicer_hu.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/hu/PrusaSlicer_hu.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/hu/PrusaSlicer_hu.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/hu/PrusaSlicer_hu.po
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko/PrusaSlicer_ko_KR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko/PrusaSlicer_ko_KR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko/PrusaSlicer_ko_KR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko/PrusaSlicer_ko_KR.po
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko.po
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko_KR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko_KR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko_KR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko_KR.po
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/nl/PrusaSlicer_nl.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/nl/PrusaSlicer_nl.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/nl/PrusaSlicer_nl.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/nl/PrusaSlicer_nl.po
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pt_BR/PrusaSlicer_pt_BR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pt_BR/PrusaSlicer_pt_BR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pt_BR/PrusaSlicer_pt_BR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pt_BR/PrusaSlicer_pt_BR.po
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ru/PrusaSlicer_ru.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ru/PrusaSlicer_ru.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ru/PrusaSlicer_ru.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ru/PrusaSlicer_ru.po
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/sl/PrusaSlicer.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/sl/PrusaSlicer.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/sl/PrusaSlicer.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/sl/PrusaSlicer.po
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/tr/PrusaSlicer_tr.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/tr/PrusaSlicer_tr.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/tr/PrusaSlicer_tr.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/tr/PrusaSlicer_tr.po
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/uk/PrusaSlicer_uk.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/uk/PrusaSlicer_uk.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/uk/PrusaSlicer_uk.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/uk/PrusaSlicer_uk.po
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_CN/PrusaSlicer_zh_CN.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_CN/PrusaSlicer_zh_CN.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_CN/PrusaSlicer_zh_CN.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_CN/PrusaSlicer_zh_CN.po
	msgmerge -N -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_TW/PrusaSlicer_zh_TW.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_TW/PrusaSlicer_zh_TW.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/PrusaSlicer.pot
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_TW/PrusaSlicer_zh_TW.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_TW/PrusaSlicer_zh_TW.po
.PHONY : gettext_merge_community_po_with_pot

# Rule to build all files generated by this target.
CMakeFiles/gettext_merge_community_po_with_pot.dir/build: gettext_merge_community_po_with_pot
.PHONY : CMakeFiles/gettext_merge_community_po_with_pot.dir/build

CMakeFiles/gettext_merge_community_po_with_pot.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/gettext_merge_community_po_with_pot.dir/cmake_clean.cmake
.PHONY : CMakeFiles/gettext_merge_community_po_with_pot.dir/clean

CMakeFiles/gettext_merge_community_po_with_pot.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles/gettext_merge_community_po_with_pot.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/gettext_merge_community_po_with_pot.dir/depend

