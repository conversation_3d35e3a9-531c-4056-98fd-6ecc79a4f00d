# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Utility rule file for gettext_concat_wx_po_with_po.

# Include any custom commands dependencies for this target.
include CMakeFiles/gettext_concat_wx_po_with_po.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/gettext_concat_wx_po_with_po.dir/progress.make

gettext_concat_wx_po_with_po: CMakeFiles/gettext_concat_wx_po_with_po.dir/build.make
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/be/PrusaSlicer_be.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/be/PrusaSlicer_be.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/be.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/be/PrusaSlicer_be.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/be/PrusaSlicer_be.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ca/PrusaSlicer_ca.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ca/PrusaSlicer_ca.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/ca.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ca/PrusaSlicer_ca.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ca/PrusaSlicer_ca.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/cs/PrusaSlicer_cs.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/cs/PrusaSlicer_cs.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/cs.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/cs/PrusaSlicer_cs.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/cs/PrusaSlicer_cs.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/de/PrusaSlicer_de.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/de/PrusaSlicer_de.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/de.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/de/PrusaSlicer_de.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/de/PrusaSlicer_de.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/en/PrusaSlicer_en.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/en/PrusaSlicer_en.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/en.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/en/PrusaSlicer_en.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/en/PrusaSlicer_en.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/es/PrusaSlicer_es.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/es/PrusaSlicer_es.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/es.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/es/PrusaSlicer_es.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/es/PrusaSlicer_es.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fi/PrusaSlicer_fi.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fi/PrusaSlicer_fi.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/fi.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fi/PrusaSlicer_fi.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fi/PrusaSlicer_fi.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fr/PrusaSlicer_fr.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fr/PrusaSlicer_fr.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/fr.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fr/PrusaSlicer_fr.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fr/PrusaSlicer_fr.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/hu/PrusaSlicer_hu.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/hu/PrusaSlicer_hu.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/hu.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/hu/PrusaSlicer_hu.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/hu/PrusaSlicer_hu.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/it/PrusaSlicer_it.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/it/PrusaSlicer_it.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/it.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/it/PrusaSlicer_it.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/it/PrusaSlicer_it.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ja/PrusaSlicer_ja.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ja/PrusaSlicer_ja.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/ja.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ja/PrusaSlicer_ja.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ja/PrusaSlicer_ja.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko/PrusaSlicer_ko_KR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko/PrusaSlicer_ko_KR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/ko.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko/PrusaSlicer_ko_KR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko/PrusaSlicer_ko_KR.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/ko_KR.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko_KR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko_KR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/ko_KR.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko_KR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko_KR.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/nl/PrusaSlicer_nl.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/nl/PrusaSlicer_nl.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/nl.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/nl/PrusaSlicer_nl.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/nl/PrusaSlicer_nl.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pl/PrusaSlicer_pl.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pl/PrusaSlicer_pl.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/pl.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pl/PrusaSlicer_pl.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pl/PrusaSlicer_pl.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pt_BR/PrusaSlicer_pt_BR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pt_BR/PrusaSlicer_pt_BR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/pt_BR.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pt_BR/PrusaSlicer_pt_BR.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pt_BR/PrusaSlicer_pt_BR.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ru/PrusaSlicer_ru.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ru/PrusaSlicer_ru.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/ru.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ru/PrusaSlicer_ru.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ru/PrusaSlicer_ru.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/sl/PrusaSlicer.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/sl/PrusaSlicer.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/sl.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/sl/PrusaSlicer.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/sl/PrusaSlicer.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/tr/PrusaSlicer_tr.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/tr/PrusaSlicer_tr.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/tr.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/tr/PrusaSlicer_tr.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/tr/PrusaSlicer_tr.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/uk/PrusaSlicer_uk.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/uk/PrusaSlicer_uk.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/uk.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/uk/PrusaSlicer_uk.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/uk/PrusaSlicer_uk.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_CN/PrusaSlicer_zh_CN.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_CN/PrusaSlicer_zh_CN.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/zh_CN.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_CN/PrusaSlicer_zh_CN.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_CN/PrusaSlicer_zh_CN.po
	msgcat --use-first -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_TW/PrusaSlicer_zh_TW.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_TW/PrusaSlicer_zh_TW.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/wx_locale/zh_TW.po
	msgattrib --no-obsolete -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_TW/PrusaSlicer_zh_TW.po /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_TW/PrusaSlicer_zh_TW.po
.PHONY : gettext_concat_wx_po_with_po

# Rule to build all files generated by this target.
CMakeFiles/gettext_concat_wx_po_with_po.dir/build: gettext_concat_wx_po_with_po
.PHONY : CMakeFiles/gettext_concat_wx_po_with_po.dir/build

CMakeFiles/gettext_concat_wx_po_with_po.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/gettext_concat_wx_po_with_po.dir/cmake_clean.cmake
.PHONY : CMakeFiles/gettext_concat_wx_po_with_po.dir/clean

CMakeFiles/gettext_concat_wx_po_with_po.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles/gettext_concat_wx_po_with_po.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/gettext_concat_wx_po_with_po.dir/depend

