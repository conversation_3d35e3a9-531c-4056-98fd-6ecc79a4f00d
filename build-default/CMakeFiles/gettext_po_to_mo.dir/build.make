# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Utility rule file for gettext_po_to_mo.

# Include any custom commands dependencies for this target.
include CMakeFiles/gettext_po_to_mo.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/gettext_po_to_mo.dir/progress.make

gettext_po_to_mo: CMakeFiles/gettext_po_to_mo.dir/build.make
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/be/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/be/PrusaSlicer_be.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ca/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ca/PrusaSlicer_ca.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/cs/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/cs/PrusaSlicer_cs.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/de/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/de/PrusaSlicer_de.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/en/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/en/PrusaSlicer_en.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/es/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/es/PrusaSlicer_es.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fi/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fi/PrusaSlicer_fi.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fr/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/fr/PrusaSlicer_fr.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/hu/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/hu/PrusaSlicer_hu.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/it/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/it/PrusaSlicer_it.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ja/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ja/PrusaSlicer_ja.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko/PrusaSlicer_ko_KR.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ko_KR/PrusaSlicer_ko_KR.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/nl/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/nl/PrusaSlicer_nl.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pl/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pl/PrusaSlicer_pl.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pt_BR/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/pt_BR/PrusaSlicer_pt_BR.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ru/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/ru/PrusaSlicer_ru.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/sl/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/sl/PrusaSlicer.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/tr/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/tr/PrusaSlicer_tr.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/uk/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/uk/PrusaSlicer_uk.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_CN/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_CN/PrusaSlicer_zh_CN.po
	msgfmt --check-format -o /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_TW/PrusaSlicer.mo /Users/<USER>/Documents/myproject/PrusaSlicer/resources/localization/zh_TW/PrusaSlicer_zh_TW.po
.PHONY : gettext_po_to_mo

# Rule to build all files generated by this target.
CMakeFiles/gettext_po_to_mo.dir/build: gettext_po_to_mo
.PHONY : CMakeFiles/gettext_po_to_mo.dir/build

CMakeFiles/gettext_po_to_mo.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/gettext_po_to_mo.dir/cmake_clean.cmake
.PHONY : CMakeFiles/gettext_po_to_mo.dir/clean

CMakeFiles/gettext_po_to_mo.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles/gettext_po_to_mo.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/gettext_po_to_mo.dir/depend

