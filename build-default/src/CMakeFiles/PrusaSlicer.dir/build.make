# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Include any dependencies generated for this target.
include src/CMakeFiles/PrusaSlicer.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/CMakeFiles/PrusaSlicer.dir/compiler_depend.make

# Include the progress variables for this target.
include src/CMakeFiles/PrusaSlicer.dir/progress.make

# Include the compile flags for this target's objects.
include src/CMakeFiles/PrusaSlicer.dir/flags.make

src/CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.o: src/CMakeFiles/PrusaSlicer.dir/flags.make
src/CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/PrusaSlicer.cpp
src/CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.o: src/CMakeFiles/PrusaSlicer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.o -MF CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.o.d -o CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/PrusaSlicer.cpp

src/CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/PrusaSlicer.cpp > CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.i

src/CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/PrusaSlicer.cpp -o CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.s

src/CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.o: src/CMakeFiles/PrusaSlicer.dir/flags.make
src/CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/PrintHelp.cpp
src/CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.o: src/CMakeFiles/PrusaSlicer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.o -MF CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.o.d -o CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/PrintHelp.cpp

src/CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/PrintHelp.cpp > CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.i

src/CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/PrintHelp.cpp -o CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.s

src/CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.o: src/CMakeFiles/PrusaSlicer.dir/flags.make
src/CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/Setup.cpp
src/CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.o: src/CMakeFiles/PrusaSlicer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object src/CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.o -MF CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.o.d -o CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/Setup.cpp

src/CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/Setup.cpp > CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.i

src/CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/Setup.cpp -o CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.s

src/CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.o: src/CMakeFiles/PrusaSlicer.dir/flags.make
src/CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/LoadPrintData.cpp
src/CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.o: src/CMakeFiles/PrusaSlicer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object src/CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.o -MF CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.o.d -o CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/LoadPrintData.cpp

src/CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/LoadPrintData.cpp > CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.i

src/CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/LoadPrintData.cpp -o CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.s

src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.o: src/CMakeFiles/PrusaSlicer.dir/flags.make
src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/ProcessTransform.cpp
src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.o: src/CMakeFiles/PrusaSlicer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.o -MF CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.o.d -o CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/ProcessTransform.cpp

src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/ProcessTransform.cpp > CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.i

src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/ProcessTransform.cpp -o CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.s

src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.o: src/CMakeFiles/PrusaSlicer.dir/flags.make
src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/ProcessActions.cpp
src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.o: src/CMakeFiles/PrusaSlicer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.o -MF CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.o.d -o CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/ProcessActions.cpp

src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/ProcessActions.cpp > CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.i

src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/ProcessActions.cpp -o CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.s

src/CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.o: src/CMakeFiles/PrusaSlicer.dir/flags.make
src/CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/Run.cpp
src/CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.o: src/CMakeFiles/PrusaSlicer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object src/CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.o -MF CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.o.d -o CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/Run.cpp

src/CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/Run.cpp > CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.i

src/CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/Run.cpp -o CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.s

src/CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.o: src/CMakeFiles/PrusaSlicer.dir/flags.make
src/CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/ProfilesSharingUtils.cpp
src/CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.o: src/CMakeFiles/PrusaSlicer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object src/CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.o -MF CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.o.d -o CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/ProfilesSharingUtils.cpp

src/CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/ProfilesSharingUtils.cpp > CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.i

src/CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/ProfilesSharingUtils.cpp -o CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.s

src/CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.o: src/CMakeFiles/PrusaSlicer.dir/flags.make
src/CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/GuiParams.cpp
src/CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.o: src/CMakeFiles/PrusaSlicer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object src/CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.o -MF CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.o.d -o CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/GuiParams.cpp

src/CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/GuiParams.cpp > CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.i

src/CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/CLI/GuiParams.cpp -o CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.s

# Object files for target PrusaSlicer
PrusaSlicer_OBJECTS = \
"CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.o" \
"CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.o" \
"CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.o" \
"CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.o" \
"CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.o" \
"CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.o" \
"CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.o" \
"CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.o" \
"CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.o"

# External object files for target PrusaSlicer
PrusaSlicer_EXTERNAL_OBJECTS =

src/PrusaSlicer: src/CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.o
src/PrusaSlicer: src/CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.o
src/PrusaSlicer: src/CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.o
src/PrusaSlicer: src/CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.o
src/PrusaSlicer: src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.o
src/PrusaSlicer: src/CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.o
src/PrusaSlicer: src/CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.o
src/PrusaSlicer: src/CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.o
src/PrusaSlicer: src/CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.o
src/PrusaSlicer: src/CMakeFiles/PrusaSlicer.dir/build.make
src/PrusaSlicer: src/libslic3r/liblibslic3r.a
src/PrusaSlicer: src/slic3r-arrange-wrapper/libslic3r-arrange-wrapper.a
src/PrusaSlicer: src/libseqarrange/liblibseqarrange.a
src/PrusaSlicer: src/slic3r/liblibslic3r_gui.a
src/PrusaSlicer: src/slic3r-arrange-wrapper/libslic3r-arrange-wrapper.a
src/PrusaSlicer: src/slic3r-arrange/libslic3r-arrange.a
src/PrusaSlicer: src/libslic3r/liblibslic3r.a
src/PrusaSlicer: src/libseqarrange/liblibseqarrange.a
src/PrusaSlicer: src/occt_wrapper/OCCTWrapper.a
src/PrusaSlicer: src/libslic3r/liblibslic3r.a
src/PrusaSlicer: src/libseqarrange/liblibseqarrange.a
src/PrusaSlicer: src/occt_wrapper/OCCTWrapper.a
src/PrusaSlicer: /opt/homebrew/lib/libboost_log.a
src/PrusaSlicer: /opt/homebrew/lib/libboost_filesystem.a
src/PrusaSlicer: /opt/homebrew/lib/libboost_locale.a
src/PrusaSlicer: /opt/homebrew/lib/libboost_thread.a
src/PrusaSlicer: /opt/homebrew/lib/libboost_charconv.a
src/PrusaSlicer: /opt/homebrew/lib/libboost_chrono.a
src/PrusaSlicer: /opt/homebrew/lib/libboost_atomic.a
src/PrusaSlicer: /opt/homebrew/lib/libboost_date_time.a
src/PrusaSlicer: /opt/homebrew/lib/libboost_nowide.a
src/PrusaSlicer: src/clipper/libclipper.a
src/PrusaSlicer: bundled_deps/glu-libtess/libglu-libtess.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libqhullcpp.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libqhullstatic_r.a
src/PrusaSlicer: src/libslic3r/liblibslic3r_cgal.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libgmpxx.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libmpfr.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libgmp.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libpng.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libjpeg.a
src/PrusaSlicer: bundled_deps/libqoi.a
src/PrusaSlicer: bundled_deps/libsemver.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libbgcode_convert.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libbgcode_binarize.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libheatshrink_dynalloc.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libbgcode_core.a
src/PrusaSlicer: bundled_deps/miniz/libminiz_static.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libopenvdb.a
src/PrusaSlicer: /opt/homebrew/lib/libboost_iostreams.a
src/PrusaSlicer: /opt/homebrew/lib/libboost_regex.a
src/PrusaSlicer: /opt/homebrew/lib/libboost_random.a
src/PrusaSlicer: /opt/homebrew/lib/libboost_system.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libHalf-2_5.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libblosc.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libz3.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libtbb.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libtbbmalloc.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnlopt.a
src/PrusaSlicer: bundled_deps/admesh/libadmesh.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKXDESTEP.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEP.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEP209.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEPAttr.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEPBase.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKXCAF.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKXSBase.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKVCAF.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKCAF.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKLCAF.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKCDF.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKV3d.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKService.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKMesh.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKBO.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKPrim.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKHLR.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKShHealing.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKTopAlgo.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKGeomAlgo.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKBRep.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKGeomBase.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKG3d.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKG2d.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKMath.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKernel.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libexpat.a
src/PrusaSlicer: bundled_deps/avrdude/libavrdude.a
src/PrusaSlicer: bundled_deps/liblocalesutils.a
src/PrusaSlicer: bundled_deps/imgui/libimgui.a
src/PrusaSlicer: src/libvgcode/liblibvgcode.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libGLEW.a
src/PrusaSlicer: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/OpenGL.tbd
src/PrusaSlicer: bundled_deps/hidapi/libhidapi.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libcurl.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libz.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libwx_osx_cocoau_gl-3.2.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libwx_osx_cocoau_webview-3.2.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libwx_osx_cocoau_html-3.2.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libwx_osx_cocoau_core-3.2.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libwx_baseu-3.2.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libz.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnanosvg.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnanosvgrast.a
src/PrusaSlicer: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libiconv.tbd
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libpng.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libz.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnanosvg.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnanosvgrast.a
src/PrusaSlicer: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libiconv.tbd
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libpng.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libjpeg.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnanosvgrast.a
src/PrusaSlicer: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnanosvg.a
src/PrusaSlicer: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libm.tbd
src/PrusaSlicer: src/CMakeFiles/PrusaSlicer.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Linking CXX executable PrusaSlicer"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/PrusaSlicer.dir/link.txt --verbose=$(VERBOSE)
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold "Symlinking the G-code viewer to PrusaSlicer, symlinking to prusa-slicer and prusa-gcodeviewer"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && ln -sf PrusaSlicer prusa-slicer
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && ln -sf PrusaSlicer prusa-gcodeviewer
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && ln -sf PrusaSlicer PrusaGCodeViewer
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold "Symlinking the resources directory into the build tree"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && ln -sfn /Users/<USER>/Documents/myproject/PrusaSlicer/resources /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/../resources

# Rule to build all files generated by this target.
src/CMakeFiles/PrusaSlicer.dir/build: src/PrusaSlicer
.PHONY : src/CMakeFiles/PrusaSlicer.dir/build

src/CMakeFiles/PrusaSlicer.dir/clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src && $(CMAKE_COMMAND) -P CMakeFiles/PrusaSlicer.dir/cmake_clean.cmake
.PHONY : src/CMakeFiles/PrusaSlicer.dir/clean

src/CMakeFiles/PrusaSlicer.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/src /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/CMakeFiles/PrusaSlicer.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/CMakeFiles/PrusaSlicer.dir/depend

