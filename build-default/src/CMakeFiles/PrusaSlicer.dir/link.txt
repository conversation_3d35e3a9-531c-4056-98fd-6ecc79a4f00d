/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/PrusaSlicer.dir/PrusaSlicer.cpp.o CMakeFiles/PrusaSlicer.dir/CLI/PrintHelp.cpp.o CMakeFiles/PrusaSlicer.dir/CLI/Setup.cpp.o CMakeFiles/PrusaSlicer.dir/CLI/LoadPrintData.cpp.o CMakeFiles/PrusaSlicer.dir/CLI/ProcessTransform.cpp.o CMakeFiles/PrusaSlicer.dir/CLI/ProcessActions.cpp.o CMakeFiles/PrusaSlicer.dir/CLI/Run.cpp.o CMakeFiles/PrusaSlicer.dir/CLI/ProfilesSharingUtils.cpp.o CMakeFiles/PrusaSlicer.dir/CLI/GuiParams.cpp.o -o PrusaSlicer   -L/Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib  -Wl,-rpath,/Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib libslic3r/liblibslic3r.a slic3r-arrange-wrapper/libslic3r-arrange-wrapper.a libseqarrange/liblibseqarrange.a -liconv -framework IOKit -framework CoreFoundation -lc++ slic3r/liblibslic3r_gui.a -framework OpenGL slic3r-arrange-wrapper/libslic3r-arrange-wrapper.a slic3r-arrange/libslic3r-arrange.a libslic3r/liblibslic3r.a libseqarrange/liblibseqarrange.a occt_wrapper/OCCTWrapper.a libslic3r/liblibslic3r.a libseqarrange/liblibseqarrange.a occt_wrapper/OCCTWrapper.a /opt/homebrew/lib/libboost_log.a /opt/homebrew/lib/libboost_filesystem.a /opt/homebrew/lib/libboost_locale.a /opt/homebrew/lib/libboost_thread.a -liconv /opt/homebrew/lib/libboost_charconv.a /opt/homebrew/lib/libboost_chrono.a /opt/homebrew/lib/libboost_atomic.a /opt/homebrew/lib/libboost_date_time.a /opt/homebrew/lib/libboost_nowide.a clipper/libclipper.a ../bundled_deps/glu-libtess/libglu-libtess.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libqhullcpp.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libqhullstatic_r.a libslic3r/liblibslic3r_cgal.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libgmpxx.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libmpfr.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libgmp.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libpng.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libjpeg.a ../bundled_deps/libqoi.a ../bundled_deps/libsemver.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libbgcode_convert.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libbgcode_binarize.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libheatshrink_dynalloc.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libbgcode_core.a ../bundled_deps/miniz/libminiz_static.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libopenvdb.a /opt/homebrew/lib/libboost_iostreams.a /opt/homebrew/lib/libboost_regex.a -lbz2 -licudata -licui18n -licuuc -llzma -lz -lzstd /opt/homebrew/lib/libboost_random.a /opt/homebrew/lib/libboost_system.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libHalf-2_5.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libblosc.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libz3.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libtbb.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libtbbmalloc.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnlopt.a ../bundled_deps/admesh/libadmesh.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKXDESTEP.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEP.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEP209.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEPAttr.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEPBase.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKXCAF.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKXSBase.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKVCAF.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKCAF.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKLCAF.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKCDF.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKV3d.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKService.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKMesh.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKBO.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKPrim.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKHLR.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKShHealing.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKTopAlgo.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKGeomAlgo.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKBRep.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKGeomBase.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKG3d.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKG2d.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKMath.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKernel.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libexpat.a -lm ../bundled_deps/avrdude/libavrdude.a ../bundled_deps/liblocalesutils.a ../bundled_deps/imgui/libimgui.a libvgcode/liblibvgcode.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libGLEW.a -Xlinker -framework -Xlinker OpenGL /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/OpenGL.tbd ../bundled_deps/hidapi/libhidapi.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libcurl.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libz.a -Xlinker -framework -Xlinker CoreFoundation -Xlinker -framework -Xlinker Security -L/Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libwx_osx_cocoau_gl-3.2.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libwx_osx_cocoau_webview-3.2.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libwx_osx_cocoau_html-3.2.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libwx_osx_cocoau_core-3.2.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libwx_baseu-3.2.a -framework WebKit -lwx_osx_cocoau_core-3.2 -lwx_baseu-3.2 /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libz.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnanosvg.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnanosvgrast.a -framework AudioToolbox /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libiconv.tbd -framework Security -framework Carbon -framework Cocoa -framework IOKit -framework QuartzCore /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libpng.a -framework WebKit -lwx_osx_cocoau_core-3.2 -lwx_baseu-3.2 /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libz.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnanosvg.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnanosvgrast.a -framework AudioToolbox /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libiconv.tbd -framework Security -framework Carbon -framework Cocoa -framework IOKit -framework QuartzCore /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libpng.a -framework CoreFoundation /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libjpeg.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnanosvgrast.a /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnanosvg.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libm.tbd -Xlinker -framework -Xlinker DiskArbitration -Xlinker -framework -Xlinker CoreWLAN 
