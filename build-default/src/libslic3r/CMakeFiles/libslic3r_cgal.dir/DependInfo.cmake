
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/CutSurface.cpp" "src/libslic3r/CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.o" "gcc" "src/libslic3r/CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/Geometry/VoronoiUtilsCgal.cpp" "src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.o" "gcc" "src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/IntersectionPoints.cpp" "src/libslic3r/CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.o" "gcc" "src/libslic3r/CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/MeshBoolean.cpp" "src/libslic3r/CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.o" "gcc" "src/libslic3r/CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/SLA/SupportIslands/VoronoiDiagramCGAL.cpp" "src/libslic3r/CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.o" "gcc" "src/libslic3r/CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/Triangulation.cpp" "src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.o" "gcc" "src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.o.d"
  "/Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/TryCatchSignal.cpp" "src/libslic3r/CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.o" "gcc" "src/libslic3r/CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
