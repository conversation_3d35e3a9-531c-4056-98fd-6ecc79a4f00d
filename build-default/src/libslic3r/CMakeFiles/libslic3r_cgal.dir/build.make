# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Include any dependencies generated for this target.
include src/libslic3r/CMakeFiles/libslic3r_cgal.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/libslic3r/CMakeFiles/libslic3r_cgal.dir/compiler_depend.make

# Include the progress variables for this target.
include src/libslic3r/CMakeFiles/libslic3r_cgal.dir/progress.make

# Include the compile flags for this target's objects.
include src/libslic3r/CMakeFiles/libslic3r_cgal.dir/flags.make

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.o: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/flags.make
src/libslic3r/CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/CutSurface.cpp
src/libslic3r/CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.o: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/libslic3r/CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libslic3r/CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.o -MF CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.o.d -o CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/CutSurface.cpp

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/CutSurface.cpp > CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.i

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/CutSurface.cpp -o CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.s

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.o: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/flags.make
src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/Geometry/VoronoiUtilsCgal.cpp
src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.o: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.o -MF CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.o.d -o CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/Geometry/VoronoiUtilsCgal.cpp

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/Geometry/VoronoiUtilsCgal.cpp > CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.i

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/Geometry/VoronoiUtilsCgal.cpp -o CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.s

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.o: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/flags.make
src/libslic3r/CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/IntersectionPoints.cpp
src/libslic3r/CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.o: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object src/libslic3r/CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libslic3r/CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.o -MF CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.o.d -o CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/IntersectionPoints.cpp

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/IntersectionPoints.cpp > CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.i

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/IntersectionPoints.cpp -o CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.s

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.o: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/flags.make
src/libslic3r/CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/MeshBoolean.cpp
src/libslic3r/CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.o: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object src/libslic3r/CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libslic3r/CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.o -MF CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.o.d -o CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/MeshBoolean.cpp

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/MeshBoolean.cpp > CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.i

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/MeshBoolean.cpp -o CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.s

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.o: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/flags.make
src/libslic3r/CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/TryCatchSignal.cpp
src/libslic3r/CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.o: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object src/libslic3r/CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libslic3r/CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.o -MF CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.o.d -o CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/TryCatchSignal.cpp

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/TryCatchSignal.cpp > CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.i

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/TryCatchSignal.cpp -o CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.s

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.o: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/flags.make
src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/Triangulation.cpp
src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.o: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.o -MF CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.o.d -o CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/Triangulation.cpp

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/Triangulation.cpp > CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.i

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/Triangulation.cpp -o CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.s

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.o: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/flags.make
src/libslic3r/CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/SLA/SupportIslands/VoronoiDiagramCGAL.cpp
src/libslic3r/CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.o: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object src/libslic3r/CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libslic3r/CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.o -MF CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.o.d -o CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/SLA/SupportIslands/VoronoiDiagramCGAL.cpp

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/SLA/SupportIslands/VoronoiDiagramCGAL.cpp > CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.i

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/SLA/SupportIslands/VoronoiDiagramCGAL.cpp -o CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.s

# Object files for target libslic3r_cgal
libslic3r_cgal_OBJECTS = \
"CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.o" \
"CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.o" \
"CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.o" \
"CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.o" \
"CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.o" \
"CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.o" \
"CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.o"

# External object files for target libslic3r_cgal
libslic3r_cgal_EXTERNAL_OBJECTS =

src/libslic3r/liblibslic3r_cgal.a: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/CutSurface.cpp.o
src/libslic3r/liblibslic3r_cgal.a: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Geometry/VoronoiUtilsCgal.cpp.o
src/libslic3r/liblibslic3r_cgal.a: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/IntersectionPoints.cpp.o
src/libslic3r/liblibslic3r_cgal.a: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/MeshBoolean.cpp.o
src/libslic3r/liblibslic3r_cgal.a: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/TryCatchSignal.cpp.o
src/libslic3r/liblibslic3r_cgal.a: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/Triangulation.cpp.o
src/libslic3r/liblibslic3r_cgal.a: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/SLA/SupportIslands/VoronoiDiagramCGAL.cpp.o
src/libslic3r/liblibslic3r_cgal.a: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/build.make
src/libslic3r/liblibslic3r_cgal.a: src/libslic3r/CMakeFiles/libslic3r_cgal.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Linking CXX static library liblibslic3r_cgal.a"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && $(CMAKE_COMMAND) -P CMakeFiles/libslic3r_cgal.dir/cmake_clean_target.cmake
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/libslic3r_cgal.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/libslic3r/CMakeFiles/libslic3r_cgal.dir/build: src/libslic3r/liblibslic3r_cgal.a
.PHONY : src/libslic3r/CMakeFiles/libslic3r_cgal.dir/build

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r && $(CMAKE_COMMAND) -P CMakeFiles/libslic3r_cgal.dir/cmake_clean.cmake
.PHONY : src/libslic3r/CMakeFiles/libslic3r_cgal.dir/clean

src/libslic3r/CMakeFiles/libslic3r_cgal.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r_cgal.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/libslic3r/CMakeFiles/libslic3r_cgal.dir/depend

