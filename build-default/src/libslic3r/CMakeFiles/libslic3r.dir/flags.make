# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# compile CXX with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++
CXX_DEFINES = -DBOOST_ASIO_DISABLE_KQUEUE -DBOOST_ATOMIC_NO_LIB -DBOOST_CHARCONV_NO_LIB -DBOOST_CHRONO_NO_LIB -DBOOST_DATE_TIME_NO_LIB -DBOOST_FILESYSTEM_NO_LIB -DBOOST_IOSTREAMS_NO_LIB -DBOOST_LOCALE_NO_LIB -DBOOST_LOG_NO_LIB -DBOOST_NOWIDE_NO_LIB -DBOOST_RANDOM_NO_LIB -DBOOST_REGEX_NO_LIB -DBOOST_SYSTEM_NO_LIB -DBOOST_THREAD_NO_LIB -DLIBNEST2D_GEOMETRIES_libslic3r -DLIBNEST2D_OPTIMIZER_nlopt -DLIBNEST2D_STATIC -DLIBNEST2D_THREADING_tbb -DOP<PERSON>VDB_OPENEXR_STATICLIB -DOPENVDB_STATICLIB -DSLIC3R_DESKTOP_INTEGRATION -DSLIC3R_GUI -DTBB_USE_CAPTURED_EXCEPTION=0 -DUNICODE -DUSE_TBB -DWXINTL_NO_GETTEXT_MACRO -D_UNICODE -DwxNO_UNSAFE_WXSTRING_CONV -DwxUSE_UNICODE

CXX_INCLUDES = -I/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/platform -I/Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/.. -I/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/libnest2d/include -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/semver -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/. -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/localesutils -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/tcbspan -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/miniz -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/agg/. -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/ankerl -I/Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/include -I/Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/include/opencascade -I/Users/<USER>/Documents/myproject/PrusaSlicer/src/clipper/. -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/glu-libtess/include -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/qoi -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/fast_float -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/int128 -isystem /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/include -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3 -isystem /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/include/LibBGCode -isystem /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/libigl/. -isystem /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/include/OpenEXR

CXX_FLAGSarm64 =  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fsigned-char -Werror=return-type -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-misleading-indentation -fno-aligned-allocation

CXX_FLAGS =  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fsigned-char -Werror=return-type -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-misleading-indentation -fno-aligned-allocation

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx.pch_OPTIONS = -Winvalid-pch;-Xclang;-emit-pch;-Xclang;-include;-Xclang;/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx;-x;c++-header

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/pchheader.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/AABBMesh.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/ArrangeHelper.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Algorithm/LineSegmentation/LineSegmentation.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Algorithm/RegionExpansion.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/BoundingBox.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/BridgeDetector.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Brim.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/BuildVolume.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/clipper.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/ClipperUtils.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Color.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Config.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/EdgeGrid.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/ElephantFootCompensation.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Emboss.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/ExPolygon.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/ExPolygonsIndex.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Extruder.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/ExtrusionEntity.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/ExtrusionEntityCollection.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/ExtrusionRole.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/ExtrusionSimulator.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Feature/Interlocking/InterlockingGenerator.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Feature/Interlocking/VoxelUtils.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Feature/FuzzySkin/FuzzySkin.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/Fill.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/Fill3DHoneycomb.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/FillAdaptive.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/FillBase.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/FillConcentric.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/FillEnsuring.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/FillHoneycomb.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/FillGyroid.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/FillPlanePath.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/FillLine.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/FillLightning.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/Lightning/DistanceField.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/Lightning/Generator.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/Lightning/Layer.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/Lightning/TreeNode.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Fill/FillRectilinear.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Flow.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Format/3mf.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Format/AMF.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Format/OBJ.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Format/objparser.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Format/STL.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Format/SLAArchiveWriter.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Format/SLAArchiveReader.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Format/ZipperArchiveImport.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Format/SL1.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Format/SL1_SVG.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Format/AnycubicSLA.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Format/STEP.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Format/SVG.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Format/SLAArchiveFormatRegistry.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Format/PrintRequest.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/ThumbnailData.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/Thumbnails.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/ConflictChecker.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/CoolingBuffer.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/ExtrusionProcessor.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/FindReplace.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/LabelObjects.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/GCodeWriter.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/PostProcessor.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/PressureEqualizer.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/PrintExtents.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/RetractWhenCrossingPerimeters.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/SpiralVase.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/SeamPlacer.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/SeamChoice.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/SeamPerimeters.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/SeamShells.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/SeamGeometry.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/SeamAligned.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/SeamRear.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/SeamRandom.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/SeamPainting.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/SeamScarf.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/ModelVisibility.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/SmoothPath.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/ToolOrdering.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/Wipe.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/WipeTower.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/WipeTowerIntegration.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/GCodeProcessor.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/GCodeMultiProcessor.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/AvoidCrossingPerimeters.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/Travels.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode/ExtrusionOrder.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCode.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/GCodeReader.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Geometry.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Geometry/ArcWelder.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Geometry/Circle.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Geometry/ConvexHull.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Geometry/MedialAxis.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Geometry/VoronoiOffset.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Geometry/VoronoiUtils.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/InfillAboveBridges.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/JumpPointSearch.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Layer.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/LayerRegion.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Line.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/BlacklistedLibraryCheck.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/CutUtils.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Model.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/ModelProcessing.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/FileReader.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/MultiMaterialSegmentation.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/MeshNormals.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Measure.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/CustomGCode.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/MultiPoint.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/NormalUtils.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/NSVGUtils.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/ObjectID.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/PerimeterGenerator.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/PlaceholderParser.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Platform.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Point.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Polygon.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/MutablePolygon.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/PolygonTrimmer.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Polyline.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Preset.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/PresetBundle.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/PrincipalComponents2D.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/AppConfig.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Print.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/PrintApply.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/PrintBase.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/PrintConfig.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/PrintObject.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/PrintObjectSlice.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/PrintRegion.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/PNGReadWrite.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/QuadricEdgeCollapse.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Semver.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/ShortEdgeCollapse.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/ShortestPath.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLAPrint.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLAPrintSteps.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Slicing.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SlicesToTriangleMesh.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SlicingAdaptive.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Subdivide.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Support/SupportCommon.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Support/SupportDebug.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Support/SupportMaterial.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Support/SupportParameters.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Support/OrganicSupport.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Support/TreeSupport.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Support/TreeSupportCommon.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Support/TreeModelVolumes.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SupportSpotsGenerator.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Surface.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SurfaceCollection.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SVG.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Tesselate.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/TriangleMesh.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/TriangleMeshSlicer.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/utils.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Time.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Timer.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Thread.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/TriangleSelector.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/TriangleSetSampling.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/TriangleSelectorWrapper.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Zipper.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/MinAreaBoundingBox.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/miniz_extension.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/MultipleBeds.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/OpenVDBUtils.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/Pad.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportTreeMesher.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportTreeBuilder.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportTree.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/Rotfinder.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SpatIndex.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/RasterBase.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/RasterToPolygons.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/ConcaveHull.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/Hollowing.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportPointGenerator.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/Clustering.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/DefaultSupportTree.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/BranchingTreeSLA.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/ZCorrection.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportIslands/EvaluateNeighbor.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportIslands/ExpandNeighbor.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportIslands/LineUtils.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportIslands/ParabolaUtils.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportIslands/PointUtils.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportIslands/PolygonUtils.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportIslands/PostProcessNeighbor.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportIslands/PostProcessNeighbors.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportIslands/SampleConfigFactory.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportIslands/SupportIslandPoint.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportIslands/UniformSupportIsland.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/SLA/SupportIslands/VoronoiGraphUtils.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/BranchingTree/BranchingTree.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/BranchingTree/PointCloud.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Arachne/BeadingStrategy/BeadingStrategy.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Arachne/BeadingStrategy/BeadingStrategyFactory.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Arachne/BeadingStrategy/DistributedBeadingStrategy.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Arachne/BeadingStrategy/LimitedBeadingStrategy.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Arachne/BeadingStrategy/OuterWallInsetBeadingStrategy.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Arachne/BeadingStrategy/RedistributeBeadingStrategy.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Arachne/BeadingStrategy/WideningBeadingStrategy.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Arachne/utils/ExtrusionLine.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Arachne/utils/SquareGrid.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Arachne/utils/PolylineStitcher.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Geometry/Voronoi.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Arachne/PerimeterOrder.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Arachne/SkeletalTrapezoidation.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Arachne/SkeletalTrapezoidationGraph.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Arachne/WallToolPaths.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Utils/DirectoriesUtils.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

# PCH options: src/libslic3r/CMakeFiles/libslic3r.dir/Utils/JsonUtils.cpp.o_OPTIONS = -Winvalid-pch;;-Xarch_arm64;-include/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch_arm64.hxx

