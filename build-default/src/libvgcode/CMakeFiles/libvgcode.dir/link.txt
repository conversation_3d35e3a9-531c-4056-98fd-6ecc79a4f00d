/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar qc liblibvgcode.a CMakeFiles/libvgcode.dir/src/Bitset.cpp.o CMakeFiles/libvgcode.dir/src/CogMarker.cpp.o CMakeFiles/libvgcode.dir/src/ColorPrint.cpp.o CMakeFiles/libvgcode.dir/src/ColorRange.cpp.o CMakeFiles/libvgcode.dir/src/ExtrusionRoles.cpp.o CMakeFiles/libvgcode.dir/src/GCodeInputData.cpp.o CMakeFiles/libvgcode.dir/src/Layers.cpp.o CMakeFiles/libvgcode.dir/src/OpenGLUtils.cpp.o CMakeFiles/libvgcode.dir/src/OptionTemplate.cpp.o CMakeFiles/libvgcode.dir/src/PathVertex.cpp.o CMakeFiles/libvgcode.dir/src/Range.cpp.o CMakeFiles/libvgcode.dir/src/SegmentTemplate.cpp.o CMakeFiles/libvgcode.dir/src/Settings.cpp.o CMakeFiles/libvgcode.dir/src/ToolMarker.cpp.o CMakeFiles/libvgcode.dir/src/Types.cpp.o CMakeFiles/libvgcode.dir/src/Utils.cpp.o CMakeFiles/libvgcode.dir/src/Viewer.cpp.o CMakeFiles/libvgcode.dir/src/ViewerImpl.cpp.o CMakeFiles/libvgcode.dir/src/ViewRange.cpp.o CMakeFiles/libvgcode.dir/glad/src/gl.c.o
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib liblibvgcode.a
