# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# compile CXX with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++
CXX_DEFINES = -DSLIC3R_DESKTOP_INTEGRATION -DSLIC3R_GUI -DUNICODE -DWXINTL_NO_GETTEXT_MACRO -D_UNICODE -DwxNO_UNSAFE_WXSTRING_CONV -DwxUSE_UNICODE

CXX_INCLUDES = -I/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/platform -I/Users/<USER>/Documents/myproject/PrusaSlicer/src/clipper/. -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/int128 -isystem /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/include -isystem /opt/homebrew/include/eigen3

CXX_FLAGSarm64 =  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fsigned-char -Werror=return-type -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-misleading-indentation

CXX_FLAGS =  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fsigned-char -Werror=return-type -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-misleading-indentation

