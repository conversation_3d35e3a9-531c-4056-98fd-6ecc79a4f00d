# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Include any dependencies generated for this target.
include src/libseqarrange/CMakeFiles/libseqarrange.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/libseqarrange/CMakeFiles/libseqarrange.dir/compiler_depend.make

# Include the progress variables for this target.
include src/libseqarrange/CMakeFiles/libseqarrange.dir/progress.make

# Include the compile flags for this target's objects.
include src/libseqarrange/CMakeFiles/libseqarrange.dir/flags.make

src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange.dir/flags.make
src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_interface.cpp
src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.o -MF CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.o.d -o CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_interface.cpp

src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_interface.cpp > CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.i

src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_interface.cpp -o CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.s

src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange.dir/flags.make
src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_preprocess.cpp
src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.o -MF CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.o.d -o CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_preprocess.cpp

src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_preprocess.cpp > CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.i

src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_preprocess.cpp -o CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.s

src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange.dir/flags.make
src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_sequential.cpp
src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.o -MF CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.o.d -o CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_sequential.cpp

src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_sequential.cpp > CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.i

src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_sequential.cpp -o CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.s

src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange.dir/flags.make
src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_utilities.cpp
src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.o -MF CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.o.d -o CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_utilities.cpp

src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_utilities.cpp > CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.i

src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/seq_utilities.cpp -o CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.s

# Object files for target libseqarrange
libseqarrange_OBJECTS = \
"CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.o" \
"CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.o" \
"CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.o" \
"CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.o"

# External object files for target libseqarrange
libseqarrange_EXTERNAL_OBJECTS =

src/libseqarrange/liblibseqarrange.a: src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.o
src/libseqarrange/liblibseqarrange.a: src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.o
src/libseqarrange/liblibseqarrange.a: src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.o
src/libseqarrange/liblibseqarrange.a: src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.o
src/libseqarrange/liblibseqarrange.a: src/libseqarrange/CMakeFiles/libseqarrange.dir/build.make
src/libseqarrange/liblibseqarrange.a: src/libseqarrange/CMakeFiles/libseqarrange.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX static library liblibseqarrange.a"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && $(CMAKE_COMMAND) -P CMakeFiles/libseqarrange.dir/cmake_clean_target.cmake
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/libseqarrange.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/libseqarrange/CMakeFiles/libseqarrange.dir/build: src/libseqarrange/liblibseqarrange.a
.PHONY : src/libseqarrange/CMakeFiles/libseqarrange.dir/build

src/libseqarrange/CMakeFiles/libseqarrange.dir/clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && $(CMAKE_COMMAND) -P CMakeFiles/libseqarrange.dir/cmake_clean.cmake
.PHONY : src/libseqarrange/CMakeFiles/libseqarrange.dir/clean

src/libseqarrange/CMakeFiles/libseqarrange.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange/CMakeFiles/libseqarrange.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/libseqarrange/CMakeFiles/libseqarrange.dir/depend

