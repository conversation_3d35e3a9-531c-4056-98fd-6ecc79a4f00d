# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# compile CXX with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++
CXX_DEFINES = -DBOOST_ASIO_DISABLE_KQUEUE -DBOOST_IOSTREAMS_NO_LIB -DBOOST_RANDOM_NO_LIB -DBOOST_REGEX_NO_LIB -DBOOST_SYSTEM_NO_LIB -DOPENVDB_OPENEXR_STATICLIB -DOPENVDB_STATICLIB -DSLIC3R_DESKTOP_INTEGRATION -DSLIC3R_GUI -DTBB_USE_CAPTURED_EXCEPTION=0 -DUNICODE -DUSE_TBB -DWXINTL_NO_GETTEXT_MACRO -D_UNICODE -DwxNO_UNSAFE_WXSTRING_CONV -DwxUSE_UNICODE

CXX_INCLUDES = -I/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/platform -I/Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/include -I/Users/<USER>/Documents/myproject/PrusaSlicer/src/libslic3r/.. -I/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libslic3r -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/semver -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/admesh/. -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/localesutils -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/tcbspan -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/miniz -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/agg/. -I/Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/ankerl -I/Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/include/opencascade -isystem /opt/homebrew/include/eigen3 -isystem /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/include -isystem /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/include/LibBGCode -isystem /Users/<USER>/Documents/myproject/PrusaSlicer/bundled_deps/libigl/. -isystem /opt/homebrew/include -isystem /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/include/OpenEXR

CXX_FLAGSarm64 =  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fsigned-char -Werror=return-type -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-misleading-indentation -fno-aligned-allocation

CXX_FLAGS =  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fsigned-char -Werror=return-type -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-misleading-indentation -fno-aligned-allocation

