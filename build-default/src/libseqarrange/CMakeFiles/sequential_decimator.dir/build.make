# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Include any dependencies generated for this target.
include src/libseqarrange/CMakeFiles/sequential_decimator.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/libseqarrange/CMakeFiles/sequential_decimator.dir/compiler_depend.make

# Include the progress variables for this target.
include src/libseqarrange/CMakeFiles/sequential_decimator.dir/progress.make

# Include the compile flags for this target's objects.
include src/libseqarrange/CMakeFiles/sequential_decimator.dir/flags.make

src/libseqarrange/CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.o: src/libseqarrange/CMakeFiles/sequential_decimator.dir/flags.make
src/libseqarrange/CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/sequential_decimator.cpp
src/libseqarrange/CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.o: src/libseqarrange/CMakeFiles/sequential_decimator.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/libseqarrange/CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libseqarrange/CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.o -MF CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.o.d -o CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/sequential_decimator.cpp

src/libseqarrange/CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/sequential_decimator.cpp > CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.i

src/libseqarrange/CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/src/sequential_decimator.cpp -o CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.s

# Object files for target sequential_decimator
sequential_decimator_OBJECTS = \
"CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.o"

# External object files for target sequential_decimator
sequential_decimator_EXTERNAL_OBJECTS =

src/libseqarrange/sequential_decimator: src/libseqarrange/CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.o
src/libseqarrange/sequential_decimator: src/libseqarrange/CMakeFiles/sequential_decimator.dir/build.make
src/libseqarrange/sequential_decimator: src/libseqarrange/liblibseqarrange.a
src/libseqarrange/sequential_decimator: src/libslic3r/liblibslic3r.a
src/libseqarrange/sequential_decimator: src/occt_wrapper/OCCTWrapper.a
src/libseqarrange/sequential_decimator: src/libseqarrange/liblibseqarrange.a
src/libseqarrange/sequential_decimator: src/libslic3r/liblibslic3r.a
src/libseqarrange/sequential_decimator: src/occt_wrapper/OCCTWrapper.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libz3.a
src/libseqarrange/sequential_decimator: /opt/homebrew/lib/libboost_log.a
src/libseqarrange/sequential_decimator: /opt/homebrew/lib/libboost_filesystem.a
src/libseqarrange/sequential_decimator: /opt/homebrew/lib/libboost_locale.a
src/libseqarrange/sequential_decimator: /opt/homebrew/lib/libboost_thread.a
src/libseqarrange/sequential_decimator: /opt/homebrew/lib/libboost_charconv.a
src/libseqarrange/sequential_decimator: /opt/homebrew/lib/libboost_chrono.a
src/libseqarrange/sequential_decimator: /opt/homebrew/lib/libboost_atomic.a
src/libseqarrange/sequential_decimator: /opt/homebrew/lib/libboost_date_time.a
src/libseqarrange/sequential_decimator: /opt/homebrew/lib/libboost_nowide.a
src/libseqarrange/sequential_decimator: src/clipper/libclipper.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libexpat.a
src/libseqarrange/sequential_decimator: bundled_deps/glu-libtess/libglu-libtess.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libqhullcpp.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libqhullstatic_r.a
src/libseqarrange/sequential_decimator: src/libslic3r/liblibslic3r_cgal.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libgmpxx.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libmpfr.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libgmp.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libpng.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libjpeg.a
src/libseqarrange/sequential_decimator: bundled_deps/libqoi.a
src/libseqarrange/sequential_decimator: bundled_deps/libsemver.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libbgcode_convert.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libbgcode_binarize.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libheatshrink_dynalloc.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libbgcode_core.a
src/libseqarrange/sequential_decimator: bundled_deps/miniz/libminiz_static.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libopenvdb.a
src/libseqarrange/sequential_decimator: /opt/homebrew/lib/libboost_iostreams.a
src/libseqarrange/sequential_decimator: /opt/homebrew/lib/libboost_regex.a
src/libseqarrange/sequential_decimator: /opt/homebrew/lib/libboost_random.a
src/libseqarrange/sequential_decimator: /opt/homebrew/lib/libboost_system.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libHalf-2_5.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libblosc.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libz.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libtbb.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libtbbmalloc.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnlopt.a
src/libseqarrange/sequential_decimator: bundled_deps/admesh/libadmesh.a
src/libseqarrange/sequential_decimator: bundled_deps/liblocalesutils.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKXDESTEP.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEP.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEP209.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEPAttr.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEPBase.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKXCAF.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKXSBase.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKVCAF.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKCAF.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKLCAF.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKCDF.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKV3d.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKService.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKMesh.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKBO.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKPrim.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKHLR.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKShHealing.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKTopAlgo.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKGeomAlgo.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKBRep.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKGeomBase.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKG3d.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKG2d.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKMath.a
src/libseqarrange/sequential_decimator: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKernel.a
src/libseqarrange/sequential_decimator: src/libseqarrange/CMakeFiles/sequential_decimator.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable sequential_decimator"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sequential_decimator.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/libseqarrange/CMakeFiles/sequential_decimator.dir/build: src/libseqarrange/sequential_decimator
.PHONY : src/libseqarrange/CMakeFiles/sequential_decimator.dir/build

src/libseqarrange/CMakeFiles/sequential_decimator.dir/clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && $(CMAKE_COMMAND) -P CMakeFiles/sequential_decimator.dir/cmake_clean.cmake
.PHONY : src/libseqarrange/CMakeFiles/sequential_decimator.dir/clean

src/libseqarrange/CMakeFiles/sequential_decimator.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange/CMakeFiles/sequential_decimator.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/libseqarrange/CMakeFiles/sequential_decimator.dir/depend

