# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

# Include any dependencies generated for this target.
include src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/compiler_depend.make

# Include the progress variables for this target.
include src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/progress.make

# Include the compile flags for this target's objects.
include src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/flags.make

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/prusaparts.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/flags.make
src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/prusaparts.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/prusaparts.cpp
src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/prusaparts.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/prusaparts.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/prusaparts.cpp.o -MF CMakeFiles/libseqarrange_tests.dir/test/prusaparts.cpp.o.d -o CMakeFiles/libseqarrange_tests.dir/test/prusaparts.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/prusaparts.cpp

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/prusaparts.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libseqarrange_tests.dir/test/prusaparts.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/prusaparts.cpp > CMakeFiles/libseqarrange_tests.dir/test/prusaparts.cpp.i

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/prusaparts.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libseqarrange_tests.dir/test/prusaparts.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/prusaparts.cpp -o CMakeFiles/libseqarrange_tests.dir/test/prusaparts.cpp.s

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_polygon.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/flags.make
src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_polygon.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_polygon.cpp
src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_polygon.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_polygon.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_polygon.cpp.o -MF CMakeFiles/libseqarrange_tests.dir/test/seq_test_polygon.cpp.o.d -o CMakeFiles/libseqarrange_tests.dir/test/seq_test_polygon.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_polygon.cpp

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_polygon.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libseqarrange_tests.dir/test/seq_test_polygon.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_polygon.cpp > CMakeFiles/libseqarrange_tests.dir/test/seq_test_polygon.cpp.i

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_polygon.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libseqarrange_tests.dir/test/seq_test_polygon.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_polygon.cpp -o CMakeFiles/libseqarrange_tests.dir/test/seq_test_polygon.cpp.s

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_sequential.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/flags.make
src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_sequential.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_sequential.cpp
src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_sequential.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_sequential.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_sequential.cpp.o -MF CMakeFiles/libseqarrange_tests.dir/test/seq_test_sequential.cpp.o.d -o CMakeFiles/libseqarrange_tests.dir/test/seq_test_sequential.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_sequential.cpp

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_sequential.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libseqarrange_tests.dir/test/seq_test_sequential.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_sequential.cpp > CMakeFiles/libseqarrange_tests.dir/test/seq_test_sequential.cpp.i

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_sequential.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libseqarrange_tests.dir/test/seq_test_sequential.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_sequential.cpp -o CMakeFiles/libseqarrange_tests.dir/test/seq_test_sequential.cpp.s

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_preprocess.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/flags.make
src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_preprocess.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_preprocess.cpp
src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_preprocess.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_preprocess.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_preprocess.cpp.o -MF CMakeFiles/libseqarrange_tests.dir/test/seq_test_preprocess.cpp.o.d -o CMakeFiles/libseqarrange_tests.dir/test/seq_test_preprocess.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_preprocess.cpp

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_preprocess.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libseqarrange_tests.dir/test/seq_test_preprocess.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_preprocess.cpp > CMakeFiles/libseqarrange_tests.dir/test/seq_test_preprocess.cpp.i

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_preprocess.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libseqarrange_tests.dir/test/seq_test_preprocess.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_preprocess.cpp -o CMakeFiles/libseqarrange_tests.dir/test/seq_test_preprocess.cpp.s

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_interface.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/flags.make
src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_interface.cpp.o: /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_interface.cpp
src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_interface.cpp.o: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_interface.cpp.o"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_interface.cpp.o -MF CMakeFiles/libseqarrange_tests.dir/test/seq_test_interface.cpp.o.d -o CMakeFiles/libseqarrange_tests.dir/test/seq_test_interface.cpp.o -c /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_interface.cpp

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_interface.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/libseqarrange_tests.dir/test/seq_test_interface.cpp.i"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_interface.cpp > CMakeFiles/libseqarrange_tests.dir/test/seq_test_interface.cpp.i

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_interface.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/libseqarrange_tests.dir/test/seq_test_interface.cpp.s"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange/test/seq_test_interface.cpp -o CMakeFiles/libseqarrange_tests.dir/test/seq_test_interface.cpp.s

# Object files for target libseqarrange_tests
libseqarrange_tests_OBJECTS = \
"CMakeFiles/libseqarrange_tests.dir/test/prusaparts.cpp.o" \
"CMakeFiles/libseqarrange_tests.dir/test/seq_test_polygon.cpp.o" \
"CMakeFiles/libseqarrange_tests.dir/test/seq_test_sequential.cpp.o" \
"CMakeFiles/libseqarrange_tests.dir/test/seq_test_preprocess.cpp.o" \
"CMakeFiles/libseqarrange_tests.dir/test/seq_test_interface.cpp.o"

# External object files for target libseqarrange_tests
libseqarrange_tests_EXTERNAL_OBJECTS =

src/libseqarrange/libseqarrange_tests: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/prusaparts.cpp.o
src/libseqarrange/libseqarrange_tests: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_polygon.cpp.o
src/libseqarrange/libseqarrange_tests: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_sequential.cpp.o
src/libseqarrange/libseqarrange_tests: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_preprocess.cpp.o
src/libseqarrange/libseqarrange_tests: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/test/seq_test_interface.cpp.o
src/libseqarrange/libseqarrange_tests: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/build.make
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libCatch2Main.a
src/libseqarrange/libseqarrange_tests: src/libseqarrange/liblibseqarrange.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libCatch2.a
src/libseqarrange/libseqarrange_tests: src/libslic3r/liblibslic3r.a
src/libseqarrange/libseqarrange_tests: src/occt_wrapper/OCCTWrapper.a
src/libseqarrange/libseqarrange_tests: src/libseqarrange/liblibseqarrange.a
src/libseqarrange/libseqarrange_tests: src/libslic3r/liblibslic3r.a
src/libseqarrange/libseqarrange_tests: src/occt_wrapper/OCCTWrapper.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libz3.a
src/libseqarrange/libseqarrange_tests: /opt/homebrew/lib/libboost_log.a
src/libseqarrange/libseqarrange_tests: /opt/homebrew/lib/libboost_filesystem.a
src/libseqarrange/libseqarrange_tests: /opt/homebrew/lib/libboost_locale.a
src/libseqarrange/libseqarrange_tests: /opt/homebrew/lib/libboost_thread.a
src/libseqarrange/libseqarrange_tests: /opt/homebrew/lib/libboost_charconv.a
src/libseqarrange/libseqarrange_tests: /opt/homebrew/lib/libboost_chrono.a
src/libseqarrange/libseqarrange_tests: /opt/homebrew/lib/libboost_atomic.a
src/libseqarrange/libseqarrange_tests: /opt/homebrew/lib/libboost_date_time.a
src/libseqarrange/libseqarrange_tests: /opt/homebrew/lib/libboost_nowide.a
src/libseqarrange/libseqarrange_tests: src/clipper/libclipper.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libexpat.a
src/libseqarrange/libseqarrange_tests: bundled_deps/glu-libtess/libglu-libtess.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libqhullcpp.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libqhullstatic_r.a
src/libseqarrange/libseqarrange_tests: src/libslic3r/liblibslic3r_cgal.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libgmpxx.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libmpfr.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libgmp.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libpng.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libjpeg.a
src/libseqarrange/libseqarrange_tests: bundled_deps/libqoi.a
src/libseqarrange/libseqarrange_tests: bundled_deps/libsemver.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libbgcode_convert.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libbgcode_binarize.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libheatshrink_dynalloc.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libbgcode_core.a
src/libseqarrange/libseqarrange_tests: bundled_deps/miniz/libminiz_static.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libopenvdb.a
src/libseqarrange/libseqarrange_tests: /opt/homebrew/lib/libboost_iostreams.a
src/libseqarrange/libseqarrange_tests: /opt/homebrew/lib/libboost_regex.a
src/libseqarrange/libseqarrange_tests: /opt/homebrew/lib/libboost_random.a
src/libseqarrange/libseqarrange_tests: /opt/homebrew/lib/libboost_system.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libHalf-2_5.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libblosc.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libz.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libtbb.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libtbbmalloc.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libnlopt.a
src/libseqarrange/libseqarrange_tests: bundled_deps/admesh/libadmesh.a
src/libseqarrange/libseqarrange_tests: bundled_deps/liblocalesutils.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKXDESTEP.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEP.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEP209.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEPAttr.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKSTEPBase.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKXCAF.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKXSBase.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKVCAF.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKCAF.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKLCAF.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKCDF.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKV3d.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKService.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKMesh.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKBO.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKPrim.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKHLR.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKShHealing.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKTopAlgo.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKGeomAlgo.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKBRep.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKGeomBase.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKG3d.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKG2d.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKMath.a
src/libseqarrange/libseqarrange_tests: /Users/<USER>/Documents/myproject/PrusaSlicer/deps/build/destdir/usr/local/lib/libTKernel.a
src/libseqarrange/libseqarrange_tests: src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking CXX executable libseqarrange_tests"
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/libseqarrange_tests.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/build: src/libseqarrange/libseqarrange_tests
.PHONY : src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/build

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange && $(CMAKE_COMMAND) -P CMakeFiles/libseqarrange_tests.dir/cmake_clean.cmake
.PHONY : src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/clean

src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/myproject/PrusaSlicer /Users/<USER>/Documents/myproject/PrusaSlicer/src/libseqarrange /Users/<USER>/Documents/myproject/PrusaSlicer/build-default /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/libseqarrange/CMakeFiles/libseqarrange_tests.dir/depend

