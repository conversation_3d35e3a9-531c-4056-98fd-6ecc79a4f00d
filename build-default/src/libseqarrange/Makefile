# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/myproject/PrusaSlicer/build-default

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/Applications/CMake.app/Contents/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/Applications/CMake.app/Contents/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/Applications/CMake.app/Contents/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/src/libseqarrange//CMakeFiles/progress.marks
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/libseqarrange/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/myproject/PrusaSlicer/build-default/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/libseqarrange/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/libseqarrange/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/libseqarrange/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
src/libseqarrange/CMakeFiles/libseqarrange.dir/rule:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/libseqarrange/CMakeFiles/libseqarrange.dir/rule
.PHONY : src/libseqarrange/CMakeFiles/libseqarrange.dir/rule

# Convenience name for target.
libseqarrange: src/libseqarrange/CMakeFiles/libseqarrange.dir/rule
.PHONY : libseqarrange

# fast build rule for target.
libseqarrange/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/libseqarrange.dir/build.make src/libseqarrange/CMakeFiles/libseqarrange.dir/build
.PHONY : libseqarrange/fast

# Convenience name for target.
src/libseqarrange/CMakeFiles/sequential_decimator.dir/rule:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/libseqarrange/CMakeFiles/sequential_decimator.dir/rule
.PHONY : src/libseqarrange/CMakeFiles/sequential_decimator.dir/rule

# Convenience name for target.
sequential_decimator: src/libseqarrange/CMakeFiles/sequential_decimator.dir/rule
.PHONY : sequential_decimator

# fast build rule for target.
sequential_decimator/fast:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/sequential_decimator.dir/build.make src/libseqarrange/CMakeFiles/sequential_decimator.dir/build
.PHONY : sequential_decimator/fast

src/seq_interface.o: src/seq_interface.cpp.o
.PHONY : src/seq_interface.o

# target to build an object file
src/seq_interface.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/libseqarrange.dir/build.make src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.o
.PHONY : src/seq_interface.cpp.o

src/seq_interface.i: src/seq_interface.cpp.i
.PHONY : src/seq_interface.i

# target to preprocess a source file
src/seq_interface.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/libseqarrange.dir/build.make src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.i
.PHONY : src/seq_interface.cpp.i

src/seq_interface.s: src/seq_interface.cpp.s
.PHONY : src/seq_interface.s

# target to generate assembly for a file
src/seq_interface.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/libseqarrange.dir/build.make src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_interface.cpp.s
.PHONY : src/seq_interface.cpp.s

src/seq_preprocess.o: src/seq_preprocess.cpp.o
.PHONY : src/seq_preprocess.o

# target to build an object file
src/seq_preprocess.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/libseqarrange.dir/build.make src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.o
.PHONY : src/seq_preprocess.cpp.o

src/seq_preprocess.i: src/seq_preprocess.cpp.i
.PHONY : src/seq_preprocess.i

# target to preprocess a source file
src/seq_preprocess.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/libseqarrange.dir/build.make src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.i
.PHONY : src/seq_preprocess.cpp.i

src/seq_preprocess.s: src/seq_preprocess.cpp.s
.PHONY : src/seq_preprocess.s

# target to generate assembly for a file
src/seq_preprocess.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/libseqarrange.dir/build.make src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_preprocess.cpp.s
.PHONY : src/seq_preprocess.cpp.s

src/seq_sequential.o: src/seq_sequential.cpp.o
.PHONY : src/seq_sequential.o

# target to build an object file
src/seq_sequential.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/libseqarrange.dir/build.make src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.o
.PHONY : src/seq_sequential.cpp.o

src/seq_sequential.i: src/seq_sequential.cpp.i
.PHONY : src/seq_sequential.i

# target to preprocess a source file
src/seq_sequential.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/libseqarrange.dir/build.make src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.i
.PHONY : src/seq_sequential.cpp.i

src/seq_sequential.s: src/seq_sequential.cpp.s
.PHONY : src/seq_sequential.s

# target to generate assembly for a file
src/seq_sequential.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/libseqarrange.dir/build.make src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_sequential.cpp.s
.PHONY : src/seq_sequential.cpp.s

src/seq_utilities.o: src/seq_utilities.cpp.o
.PHONY : src/seq_utilities.o

# target to build an object file
src/seq_utilities.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/libseqarrange.dir/build.make src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.o
.PHONY : src/seq_utilities.cpp.o

src/seq_utilities.i: src/seq_utilities.cpp.i
.PHONY : src/seq_utilities.i

# target to preprocess a source file
src/seq_utilities.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/libseqarrange.dir/build.make src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.i
.PHONY : src/seq_utilities.cpp.i

src/seq_utilities.s: src/seq_utilities.cpp.s
.PHONY : src/seq_utilities.s

# target to generate assembly for a file
src/seq_utilities.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/libseqarrange.dir/build.make src/libseqarrange/CMakeFiles/libseqarrange.dir/src/seq_utilities.cpp.s
.PHONY : src/seq_utilities.cpp.s

src/sequential_decimator.o: src/sequential_decimator.cpp.o
.PHONY : src/sequential_decimator.o

# target to build an object file
src/sequential_decimator.cpp.o:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/sequential_decimator.dir/build.make src/libseqarrange/CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.o
.PHONY : src/sequential_decimator.cpp.o

src/sequential_decimator.i: src/sequential_decimator.cpp.i
.PHONY : src/sequential_decimator.i

# target to preprocess a source file
src/sequential_decimator.cpp.i:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/sequential_decimator.dir/build.make src/libseqarrange/CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.i
.PHONY : src/sequential_decimator.cpp.i

src/sequential_decimator.s: src/sequential_decimator.cpp.s
.PHONY : src/sequential_decimator.s

# target to generate assembly for a file
src/sequential_decimator.cpp.s:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(MAKE) $(MAKESILENT) -f src/libseqarrange/CMakeFiles/sequential_decimator.dir/build.make src/libseqarrange/CMakeFiles/sequential_decimator.dir/src/sequential_decimator.cpp.s
.PHONY : src/sequential_decimator.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... libseqarrange"
	@echo "... sequential_decimator"
	@echo "... src/seq_interface.o"
	@echo "... src/seq_interface.i"
	@echo "... src/seq_interface.s"
	@echo "... src/seq_preprocess.o"
	@echo "... src/seq_preprocess.i"
	@echo "... src/seq_preprocess.s"
	@echo "... src/seq_sequential.o"
	@echo "... src/seq_sequential.i"
	@echo "... src/seq_sequential.s"
	@echo "... src/seq_utilities.o"
	@echo "... src/seq_utilities.i"
	@echo "... src/seq_utilities.s"
	@echo "... src/sequential_decimator.o"
	@echo "... src/sequential_decimator.i"
	@echo "... src/sequential_decimator.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /Users/<USER>/Documents/myproject/PrusaSlicer/build-default && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

